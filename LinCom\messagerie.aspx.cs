﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Diagnostics.Eventing.Reader;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.IO;

namespace LinCom
{
    public partial class messagerie : System.Web.UI.Page
    {
        IMessage objmes = new MessageImp();
        Message_Class mess = new Message_Class();
        IConversation objconver = new ConversationImp();
        Conversation_Class conver = new Conversation_Class();
        ParticipantConversation_Class partconver = new ParticipantConversation_Class();
        MessageStatus_Class messtatu = new MessageStatus_Class();

        Organisation_Class org = new Organisation_Class();
        Organisation_Class orga = new Organisation_Class();
        IOrganisation objorg = new OrganisationImp();
        IPoste objpost = new PosteImp();
        Post_Class post = new Post_Class();
        Post_Class pos = new Post_Class();
        IDomainePost objdompost = new DomainePostImp();
        DomainePost_Class dompost = new DomainePost_Class();
        Membre_Class mem = new Membre_Class();
        IMembre objmem = new MembreImp();
        IPartenaire objpart = new PartenaireImp();
        Partenaire_Class part = new Partenaire_Class();
        DomaineIntervention_Class actdom = new DomaineIntervention_Class();
        IDomaineIntervention objactdom = new DomaineInterventionImp();
        ICommonCode co = new CommonCode();
        //   UrlPartage = $"{Request.Url.GetLeftPart(UriPartial.Authority)}/post/" + ep.name,
        IDomaineInterventionOrganisation objdomorg = new DomaineInterventionOrganisationImp();
        DomaineInterventionOrganisation_Class domorg = new DomaineInterventionOrganisation_Class();
        int info;
        static string imge, imge1, pdfe, nameorg;
       long ide; static long idorg;
        static int rolid;
        long index;
        static long conversationreceveur;
        protected void Page_Load(object sender, EventArgs e)
        {
            HttpCookie role = Request.Cookies["role"];
            HttpCookie usernm = Request.Cookies["usernm"];
            HttpCookie idperso = Request.Cookies["iduser"];

            if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
            {//admin
                long.TryParse(Request.Cookies["iduser"].Value, out ide);//idconnecte

            }
            if (!IsPostBack)
            {
                // Par défaut, tu peux initialiser avec une conversation
                
                ChargerMessages();
                AppelMethode();
            }
        }
        public void AppelMethode()
        {
            objmem.ChargerListview(listmembre,-1,"actif","");

          
        }
     

        protected void listmembre_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            if (e.CommandName == "viewmem")
            {
                long idMembre =Convert.ToInt64( e.CommandArgument.ToString());
                // Utilisez l'ID pour récupérer les détails du membre
                objmem.AfficherDetails(idMembre,mem);
               
                // Changez le titre de la discussion
                lblHeader.Text = "Message envoyé à " + mem.Nom+" "+mem.Prenom;
                lblId.Text = mem.MembreId.ToString();

                ChargerMessages();

            }
        }

    private void CreationConversation(int cd,string sujetgroup)
        {//creation d'une nouvelle convrsation

            if (cd==0)
            {//privee
                conver.Sujet = "";
                conver.IsGroup = 0;
                conver.CreatedAt = DateTime.Now;
                objconver.Creer(conver);
            }
            else if (cd==1)
            {
                //equipe
                conver.Sujet = sujetgroup;
                conver.IsGroup = 1;
                conver.CreatedAt = DateTime.Now;
                objconver.Creer(conver);
            }
              
        }
      private  void CreationParticipantConversation(long memparticipant)
        {//creation des membres qui commencent le tchat
         //long conversationreceveur = objconver.VerifierConversationId(ide, Convert.ToInt64(memparticipant));
         //if (conversationreceveur > 0)
         //{
         //    partconver.ConversationId = conversationreceveur;
         //    partconver.MembreId= memparticipant;
         //    partconver.JoinedAt = DateTime.Now;

            //    objconver.AjouterParticipant(conversationreceveur, Convert.ToInt64(memparticipant));

            //}
            conversationreceveur = objconver.VerifierConversationId(ide, memparticipant);

            // Si aucune conversation => on la crée
            if (conversationreceveur <= 0)
            {
                CreationConversation(0, ""); // conversation privée
                conversationreceveur = objconver.VerifierConversationId(ide, memparticipant);
            }
            else
            {
                partconver.ConversationId = conversationreceveur;
                partconver.MembreId = memparticipant;
                partconver.JoinedAt = DateTime.Now;
                // Ensuite on ajoute les 2 participants S'ILS NE SONT PAS DÉJÀ DEDANS

                if (!objconver.ParticipantExiste(conversationreceveur, ide))
                    objconver.AjouterParticipant(conversationreceveur, ide);

                if (!objconver.ParticipantExiste(conversationreceveur, memparticipant))
                    objconver.AjouterParticipant(conversationreceveur, memparticipant);

            }

        }
        private int CreationMessage(long convID,long membrId)
        {
            mess.ConversationId = convID;
            mess.SenderId = membrId;
            mess.Contenu = txtMessage.Text;
            mess.DateEnvoi = DateTime.Now;
            mess.name = "";
            mess.AttachmentUrl = "";
            info=objmes.Envoyer(mess);

            return info;

        }
        private int CreationMessagestatus(long convID, long membrId,int lire)
        {
            messtatu.MessageId = convID;
            messtatu.UserId = membrId;
            messtatu.IsRead = lire;
            messtatu.ReadAt = DateTime.Now;
           
            info =objmes.EnvoyerMessageStatus(messtatu);

            return info;
        }

        private void EnvoieMessagerie()
        {
            if (!string.IsNullOrWhiteSpace(txtMessage.Text))
            {
                long senderId = ide;
                long destinataireId = Convert.ToInt64(lblId.Text);
                bool isGroup = hdnIsGroup.Value == "1";
                int info=0,info1 = 0,info2=0;

                if (isGroup)
                {//il faut continuer l'implementation
                    // Groupe : conversation déjà existante via id du groupe
                    long idGroupe = destinataireId;

                    // Ici, on ajoute le message à la table des messages de groupe
                  ///  info = objmes.AjouterMessageDansGroupe(idGroupe, senderId, txtMessage.Text);
                  //  if (info == 1)
                   //     objmes.ChargerMessagesGroupe(rptMessages, idGroupe, 50);
                }
                else
                {
                    CreationParticipantConversation(destinataireId);

                    // Tchat privé
                    long conversationId = objconver.VerifierConversationId(senderId, destinataireId);

                    //if (conversationId <= 0)
                    //    CreationConversation(0, "");

                   
                   // conversationId = objconver.VerifierConversationId(senderId, destinataireId);

                    info=CreationMessage(conversationId,senderId);
                    info1=CreationMessagestatus(conversationId,senderId,1);
                    info2=CreationMessagestatus(conversationId,destinataireId,0);

                    if (info == 1 && info1==1 && info2==1)
                        ChargerMessages();
                }

                if (info != 1)
                    Response.Write("<script>alert('Erreur lors de l’envoi du message.');</script>");
            }
        }
        protected void btnenvoie_ServerClick(object sender, EventArgs e)
        {
            try
            {
                if (!string.IsNullOrEmpty(txtMessage.Text.Trim()) && !string.IsNullOrEmpty(lblId.Text) && lblId.Text != "0")
                {
                    long senderId = ide;
                    long destinataireId = Convert.ToInt64(lblId.Text);
                    long conversationId = objconver.VerifierConversationId(senderId, destinataireId);

                    string contenuMessage = NettoyerMessage(txtMessage.Text.Trim());
                    string attachmentUrl = "";

                    // Gestion des pièces jointes
                    if (fileUpload != null && fileUpload.HasFile)
                    {
                        try
                        {
                            string dossierDestination = Server.MapPath("~/file/messages/");
                            attachmentUrl = objmes.SauvegarderFichierJoint(fileUpload.PostedFile, dossierDestination);
                            if (lblFileName != null)
                                lblFileName.Text = fileUpload.FileName;
                        }
                        catch (Exception ex)
                        {
                            Response.Write($"<script>alert('Erreur lors du téléchargement du fichier: {ex.Message}');</script>");
                            return;
                        }
                    }

                    // Envoi du message
                    mess.ConversationId = conversationId;
                    mess.SenderId = senderId;
                    mess.Contenu = contenuMessage;
                    mess.AttachmentUrl = attachmentUrl;
                    mess.name = mem.Nom + " " + mem.Prenom;
                    mess.DateEnvoi = DateTime.Now;

                    int resultat = objmes.Envoyer(mess);

                    if (resultat > 0)
                    {
                        // Marquer le message comme lu pour l'expéditeur
                        objmes.MarquerCommeLu(mess.MessageId, senderId);

                        // Vider les champs
                        txtMessage.Text = "";
                        if (lblFileName != null)
                            lblFileName.Text = "";

                        // Recharger les messages
                        ChargerMessages();

                        Response.Write("<script>scrollToBottom();</script>");
                    }
                    else
                    {
                        Response.Write("<script>alert('Erreur lors de l\\'envoi du message');</script>");
                    }
                }
                else
                {
                    Response.Write("<script>alert('Veuillez saisir un message et sélectionner un destinataire');</script>");
                }
            }
            catch (Exception ex)
            {
                Response.Write($"<script>alert('Erreur: {ex.Message}');</script>");
            }
        }
        void EnvoieMessage()
        {
            if (!string.IsNullOrWhiteSpace(txtMessage.Text))
            {
                long conversationreceveurmembre = objconver.VerifierConversationId(ide, Convert.ToInt64(lblId.Text));
               
                if (conversationreceveurmembre <= 0)
                    CreationConversation(0,"");

                long conversationreceveurmembreencore = objconver.VerifierConversationId(ide, Convert.ToInt64(lblId.Text));

                mess.ConversationId = conversationreceveurmembreencore;
                mess.SenderId = ide;
                mess.Contenu = txtMessage.Text.Trim();
                mess.DateEnvoi = DateTime.Now;
                mess.name = "";
                mess.AttachmentUrl = null;

               info= objmes.Envoyer(mess);

                if (info==1)
                {
                    //CreationParticipantConversation();

                 //   Response.Write("<script LANGUAGE=JavaScript>alert('Message envoyé')</script>");

                }
                else
                {
                    Response.Write("<script LANGUAGE=JavaScript>alert('Erreur')</script>");


                }
            }
        }
        void ChargerMessages()
        {
            //chargement des messages
            long senderId = ide;
            long destinataireId = Convert.ToInt64(lblId.Text);
            long conversationId = objconver.VerifierConversationId(senderId, destinataireId);

            objmes.ChargerMessages(rptMessages, conversationId, 1000);
        }

        // Nouvelles méthodes pour les fonctionnalités améliorées

        // Méthodes de recherche
        protected void txtRechercheContact_TextChanged(object sender, EventArgs e)
        {
            string motCle = txtRechercheContact.Text.Trim();
            if (!string.IsNullOrEmpty(motCle))
            {
                objmem.RechercherMembres(listmembre, motCle, "actif");
            }
            else
            {
                objmem.ChargerListview(listmembre, -1, "actif", "");
            }
        }

        protected void btnRechercheContact_Click(object sender, EventArgs e)
        {
            txtRechercheContact_TextChanged(sender, e);
        }

        protected void btnRechercheMessage_Click(object sender, EventArgs e)
        {
            string motCle = txtRechercheMessage.Text.Trim();
            if (!string.IsNullOrEmpty(motCle) && !string.IsNullOrEmpty(lblId.Text) && lblId.Text != "0")
            {
                long senderId = ide;
                long destinataireId = Convert.ToInt64(lblId.Text);
                long conversationId = objconver.VerifierConversationId(senderId, destinataireId);

                objmes.RechercherMessages(rptMessages, conversationId, motCle, 100);
            }
        }



        /// <summary>
        /// Méthode pour marquer tous les messages d'une conversation comme lus
        /// </summary>
        private void MarquerMessagesCommeLus(long conversationId, long userId)
        {
            try
            {
                objmes.MarquerTousCommeLus(conversationId, userId);
            }
            catch (Exception ex)
            {
                // Log l'erreur mais ne pas interrompre le flux
                System.Diagnostics.Debug.WriteLine($"Erreur lors du marquage des messages comme lus: {ex.Message}");
            }
        }

        /// <summary>
        /// Méthode pour valider les données d'entrée
        /// </summary>
        private bool ValiderDonnees(string message, string destinataireId)
        {
            if (string.IsNullOrWhiteSpace(message))
            {
                Response.Write("<script>alert('Le message ne peut pas être vide');</script>");
                return false;
            }

            if (message.Length > 1000)
            {
                Response.Write("<script>alert('Le message est trop long (maximum 1000 caractères)');</script>");
                return false;
            }

            if (string.IsNullOrEmpty(destinataireId) || destinataireId == "0")
            {
                Response.Write("<script>alert('Veuillez sélectionner un destinataire');</script>");
                return false;
            }

            return true;
        }

        /// <summary>
        /// Méthode pour nettoyer et sécuriser le contenu du message
        /// </summary>
        private string NettoyerMessage(string message)
        {
            if (string.IsNullOrEmpty(message)) return "";

            // Supprimer les scripts malveillants
            message = System.Text.RegularExpressions.Regex.Replace(message, @"<script[^>]*>.*?</script>", "", System.Text.RegularExpressions.RegexOptions.IgnoreCase);

            // Encoder les caractères HTML pour éviter les injections
            message = HttpUtility.HtmlEncode(message);

            // Décoder pour permettre les emojis
            message = HttpUtility.HtmlDecode(message);

            return message.Trim();
        }

        /// <summary>
        /// Méthodes helper pour l'affichage dans les templates
        /// </summary>
        protected long GetCurrentUserId()
        {
            return ide;
        }

        protected string GetAttachmentIcon(string attachmentType)
        {
            switch (attachmentType?.ToLower())
            {
                case "image":
                    return "🖼️";
                case "pdf":
                    return "📕";
                case "word":
                    return "📘";
                case "excel":
                    return "📗";
                case "powerpoint":
                    return "📙";
                case "archive":
                    return "🗜️";
                case "video":
                    return "🎬";
                case "audio":
                    return "🎵";
                default:
                    return "📄";
            }
        }

        protected string FormatFileSize(long bytes)
        {
            return MessageUtility_Class.FormaterTailleFichier(bytes);
        }

        protected string GetFileIcon(string fileName)
        {
            return MessageUtility_Class.ObtenirIconeFichier(fileName);
        }

    }
}