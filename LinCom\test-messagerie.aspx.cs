using LinCom.Classe;
using System;
using System.IO;
using System.Web;
using System.Web.UI;

namespace LinCom
{
    public partial class test_messagerie : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                AfficherInfosSysteme();
            }
        }

        private void AfficherInfosSysteme()
        {
            try
            {
                string info = $@"
                    <strong>Serveur:</strong> {Environment.MachineName}<br/>
                    <strong>Version .NET:</strong> {Environment.Version}<br/>
                    <strong>Dossier App:</strong> {Server.MapPath("~")}<br/>
                    <strong>Dossier Messages:</strong> {Server.MapPath("~/file/messages/")}<br/>
                    <strong>Taille max fichier:</strong> {MessageUtility_Class.TailleMaxFichier / (1024 * 1024)} MB<br/>
                    <strong>Extensions autorisées:</strong> {string.Join(", ", MessageUtility_Class.ExtensionsAutorisees)}
                ";
                lblSystemInfo.Text = info;
            }
            catch (Exception ex)
            {
                lblSystemInfo.Text = $"<span class='error'>Erreur: {ex.Message}</span>";
            }
        }

        protected void btnTestFile_Click(object sender, EventArgs e)
        {
            try
            {
                if (fileUploadTest.HasFile)
                {
                    string resultat = MessageUtility_Class.ValiderFichier(fileUploadTest.PostedFile);
                    
                    if (string.IsNullOrEmpty(resultat))
                    {
                        lblFileResult.Text = "✅ Fichier valide !";
                        lblFileResult.CssClass = "test-result success";
                        
                        // Afficher les détails du fichier
                        string details = $@"
                            <br/><strong>Nom:</strong> {fileUploadTest.FileName}
                            <br/><strong>Taille:</strong> {MessageUtility_Class.FormaterTailleFichier(fileUploadTest.PostedFile.ContentLength)}
                            <br/><strong>Type:</strong> {fileUploadTest.PostedFile.ContentType}
                            <br/><strong>Icône:</strong> {MessageUtility_Class.ObtenirIconeFichier(fileUploadTest.FileName)}
                        ";
                        lblFileResult.Text += details;
                    }
                    else
                    {
                        lblFileResult.Text = $"❌ {resultat}";
                        lblFileResult.CssClass = "test-result error";
                    }
                }
                else
                {
                    lblFileResult.Text = "⚠️ Aucun fichier sélectionné";
                    lblFileResult.CssClass = "test-result error";
                }
            }
            catch (Exception ex)
            {
                lblFileResult.Text = $"❌ Erreur: {ex.Message}";
                lblFileResult.CssClass = "test-result error";
            }
        }

        protected void btnTestEmoji_Click(object sender, EventArgs e)
        {
            try
            {
                string texteOriginal = txtEmojiTest.Text;
                string texteConverti = MessageUtility_Class.ConvertirEmojis(texteOriginal);
                
                lblEmojiResult.Text = $@"
                    <br/><strong>Original:</strong> {HttpUtility.HtmlEncode(texteOriginal)}
                    <br/><strong>Converti:</strong> {texteConverti}
                ";
            }
            catch (Exception ex)
            {
                lblEmojiResult.Text = $"❌ Erreur: {ex.Message}";
            }
        }

        protected void btnTestClean_Click(object sender, EventArgs e)
        {
            try
            {
                string texteOriginal = txtCleanTest.Text;
                string texteNettoye = MessageUtility_Class.NettoyerMessage(texteOriginal);
                
                lblCleanResult.Text = $@"
                    <br/><strong>Original:</strong> {HttpUtility.HtmlEncode(texteOriginal)}
                    <br/><strong>Nettoyé:</strong> {HttpUtility.HtmlEncode(texteNettoye)}
                ";
            }
            catch (Exception ex)
            {
                lblCleanResult.Text = $"❌ Erreur: {ex.Message}";
            }
        }

        protected void btnTestFileName_Click(object sender, EventArgs e)
        {
            try
            {
                string nomOriginal = txtFileNameTest.Text;
                if (!string.IsNullOrEmpty(nomOriginal))
                {
                    string nomUnique = MessageUtility_Class.GenererNomFichierUnique(nomOriginal);
                    
                    lblFileNameResult.Text = $@"
                        <br/><strong>Original:</strong> {HttpUtility.HtmlEncode(nomOriginal)}
                        <br/><strong>Unique:</strong> {nomUnique}
                    ";
                }
                else
                {
                    lblFileNameResult.Text = "⚠️ Veuillez saisir un nom de fichier";
                }
            }
            catch (Exception ex)
            {
                lblFileNameResult.Text = $"❌ Erreur: {ex.Message}";
            }
        }

        protected void btnTestIcon_Click(object sender, EventArgs e)
        {
            try
            {
                string nomFichier = txtIconTest.Text;
                if (!string.IsNullOrEmpty(nomFichier))
                {
                    string icone = MessageUtility_Class.ObtenirIconeFichier(nomFichier);
                    string extension = Path.GetExtension(nomFichier).ToLower();
                    
                    lblIconResult.Text = $@"
                        <br/><strong>Fichier:</strong> {HttpUtility.HtmlEncode(nomFichier)}
                        <br/><strong>Extension:</strong> {extension}
                        <br/><strong>Icône:</strong> {icone} ({icone})
                    ";
                }
                else
                {
                    lblIconResult.Text = "⚠️ Veuillez saisir un nom de fichier";
                }
            }
            catch (Exception ex)
            {
                lblIconResult.Text = $"❌ Erreur: {ex.Message}";
            }
        }

        protected void btnTestSize_Click(object sender, EventArgs e)
        {
            try
            {
                string tailleText = txtSizeTest.Text;
                if (long.TryParse(tailleText, out long taille))
                {
                    string tailleFormatee = MessageUtility_Class.FormaterTailleFichier(taille);
                    
                    lblSizeResult.Text = $@"
                        <br/><strong>Bytes:</strong> {taille:N0}
                        <br/><strong>Formaté:</strong> {tailleFormatee}
                    ";
                }
                else
                {
                    lblSizeResult.Text = "⚠️ Veuillez saisir un nombre valide";
                }
            }
            catch (Exception ex)
            {
                lblSizeResult.Text = $"❌ Erreur: {ex.Message}";
            }
        }
    }
}
