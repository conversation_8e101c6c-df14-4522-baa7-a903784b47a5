﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;
using System.IO;
using System.Text.RegularExpressions;
using System.Data.Entity;

namespace LinCom.Imp
{
    public class MessageImp : IMessage
    {
        int msg;
        private Message message = new Message();
        private MessageStatu mesast= new MessageStatu();

        public void AfficherDetails(long messageId, Message_Class messageClass)
        {
            using (Connection con = new Connection())
            {
                var m = con.Messages.FirstOrDefault(x => x.MessageId == messageId);
                if (m != null)
                {
                    messageClass.MessageId = m.MessageId;
                    messageClass.ConversationId = m.ConversationId;
                    messageClass.SenderId = m.SenderId;
                    messageClass.Contenu = m.Contenu;
                    messageClass.AttachmentUrl = m.AttachmentUrl;
                    messageClass.DateEnvoi = m.DateEnvoi;
                    messageClass.name = m.name;

    }
            }
        }

      

        public void ChargerMessages(Repeater rpt, long conversationId, int nombreMessages)
        {
            using (Connection con = new Connection())
            {
                var messages = from m in con.Messages
                               join mb in con.Membres on m.SenderId equals mb.MembreId
                               where m.ConversationId == conversationId
                               orderby m.DateEnvoi descending
                               select new
                               {
                                   id = m.MessageId,
                                   Contenu = ProcessEmojis(m.Contenu ?? ""),
                                   Expediteur = (mb.Nom ?? "") + " " + (mb.Prenom ?? ""),
                                   Photomembre = mb.PhotoProfil ?? "default-avatar.png",
                                   DateEnvoi = m.DateEnvoi,
                                   name = m.name ?? "",
                                   AttachmentUrl = m.AttachmentUrl ?? "",
                                   AttachmentName = GetAttachmentName(m.AttachmentUrl),
                                   AttachmentType = GetAttachmentType(m.AttachmentUrl),
                                   SenderId = m.SenderId

                               };

                rpt.DataSource = messages.Take(nombreMessages).ToList();
                rpt.DataBind();
            }
        }

        public int CompterNonLus(long membreId)
        {
            using (Connection con = new Connection())
            {
                return con.Messages
                    .Count(m => m.SenderId != membreId &&
                               con.ParticipantConversations.Any(p =>
                                   p.ConversationId == m.ConversationId &&
                                   p.MembreId == membreId));
            }
        }

        public int Envoyer(Message_Class messageClass)
        {
            using (Connection con = new Connection())
            {
                message.ConversationId = messageClass.ConversationId;
                message.SenderId = messageClass.SenderId;
                message.Contenu = messageClass.Contenu;
                message.DateEnvoi = DateTime.Now;
                message.name = messageClass.name;
                message.AttachmentUrl=messageClass.AttachmentUrl;

                try
                {
                    con.Messages.Add(message);
                    return con.SaveChanges();
                }
                catch
                {
                    return 0;
                }
            }
        }

        public int EnvoyerMessageStatus(MessageStatus_Class messageClass)
        {
            using (Connection con = new Connection())
            {
                mesast.MessageId = messageClass.MessageId;
                mesast.UserId = messageClass.UserId;
                mesast.IsRead = messageClass.IsRead;
                mesast.ReadAt = DateTime.Now;
              
                try
                {
                    con.MessageStatus.Add(mesast);
                    return con.SaveChanges();
                }
                catch
                {
                    return 0;
                }
            }
        }



        public int Modifier(Message_Class messageClass)
        {
            using (Connection con = new Connection())
            {
                var m = con.Messages.FirstOrDefault(x => x.MessageId == messageClass.MessageId);
                if (m != null)
                {
                    m.Contenu = messageClass.Contenu;
                    m.name = messageClass.name;

                    try
                    {
                        return con.SaveChanges();
                    }
                    catch
                    {
                        return 0;
                    }
                }
                return 0;
            }
        }

        public int Supprimer(long messageId)
        {
            using (Connection con = new Connection())
            {
                var m = con.Messages.FirstOrDefault(x => x.MessageId == messageId);
                if (m != null)
                {
                    con.Messages.Remove(m);
                    try
                    {
                        return con.SaveChanges();
                    }
                    catch
                    {
                        return 0;
                    }
                }
                return 0;
            }
        }


        //Methodes pour Messages Statut

        public void AfficherDetailsMessageStatut(long statusId, MessageStatus_Class statusClass)
        {
            using (Connection con = new Connection())
            {
                var status = con.MessageStatus.FirstOrDefault(x => x.MessagestatusID == statusId);
                if (status != null)
                {
                    statusClass.MessagestatusID = status.MessagestatusID;
                    statusClass.MessageId = status.MessageId;
                    statusClass.UserId = status.UserId;
                    statusClass.IsRead = status.IsRead;
                    statusClass.ReadAt = status.ReadAt;
                }
            }
        }

        public int MarquerCommeLu(long messageId, long userId)
        {
            using (Connection con = new Connection())
            {
                var status = con.MessageStatus.FirstOrDefault(x => x.MessageId == messageId && x.UserId == userId);
                if (status != null)
                {
                    status.IsRead = 1;
                    status.ReadAt = DateTime.Now;
                    return con.SaveChanges();
                }
                return 0;
            }
        }

        public int AjouterMessageEtStatusPourTous(long conversationId, long senderId, string contenu, string attachmentUrl = null)
        {
            using (var con = new Connection())
            {
                using (var transaction = con.Database.BeginTransaction())
                {
                    try
                    {
                        // 1. Création et ajout du message
                        var message = new Message
                        {
                            ConversationId = conversationId,
                            SenderId = senderId,
                            Contenu = contenu,
                            AttachmentUrl = attachmentUrl,
                            DateEnvoi = DateTime.Now,
                            name = "Nom ou pseudo de l'expéditeur" // adapte selon ton contexte
                        };

                        con.Messages.Add(message);
                        con.SaveChanges(); // Génère MessageId

                        // 2. Récupérer tous les participants de la conversation
                        var participants = con.ParticipantConversations
                                              .Where(pc => pc.ConversationId == conversationId)
                                              .Select(pc => pc.MembreId)
                                              .ToList();

                        // 3. Créer les MessageStatus pour tous
                        foreach (var membreId in participants)
                        {
                            var status = new MessageStatu
                            {
                                MessageId = message.MessageId,
                                UserId = (long)membreId,
                                IsRead = (membreId == senderId) ? 1 : 0,
                                ReadAt = (membreId == senderId) ? (DateTime?)DateTime.Now : null
                            };
                            con.MessageStatus.Add(status);
                        }

                        con.SaveChanges();
                        transaction.Commit();

                        return 1; // succès
                    }
                    catch
                    {
                        transaction.Rollback();
                        return 0; // échec
                    }
                }
            }
        }


        public int SupprimerMessageStatut(long messageStatusId)
        {
            using (Connection con = new Connection())
            {
                var status = con.MessageStatus.FirstOrDefault(x => x.MessagestatusID == messageStatusId);
                if (status != null)
                {
                    con.MessageStatus.Remove(status);
                    return con.SaveChanges();
                }
                return 0;
            }
        }

        // Nouvelles méthodes pour améliorer la messagerie

        /// <summary>
        /// Recherche des messages dans une conversation
        /// </summary>
        public void RechercherMessages(Repeater rpt, long conversationId, string motCle, int nombreMessages = 50)
        {
            using (Connection con = new Connection())
            {
                var messages = from m in con.Messages
                               join mb in con.Membres on m.SenderId equals mb.MembreId
                               where m.ConversationId == conversationId &&
                                     (m.Contenu.Contains(motCle) || mb.Nom.Contains(motCle) || mb.Prenom.Contains(motCle))
                               orderby m.DateEnvoi descending
                               select new
                               {
                                   id = m.MessageId,
                                   Contenu = ProcessEmojis(m.Contenu ?? ""),
                                   Expediteur = (mb.Nom ?? "") + " " + (mb.Prenom ?? ""),
                                   Photomembre = mb.PhotoProfil ?? "default-avatar.png",
                                   DateEnvoi = m.DateEnvoi,
                                   name = m.name ?? "",
                                   AttachmentUrl = m.AttachmentUrl ?? "",
                                   AttachmentName = GetAttachmentName(m.AttachmentUrl),
                                   AttachmentType = GetAttachmentType(m.AttachmentUrl),
                                   SenderId = m.SenderId
                               };

                rpt.DataSource = messages.Take(nombreMessages).ToList();
                rpt.DataBind();
            }
        }

        /// <summary>
        /// Traite les emojis dans le texte
        /// </summary>
        private string ProcessEmojis(string texte)
        {
            if (string.IsNullOrEmpty(texte)) return texte;

            // Dictionnaire des emojis courants
            var emojis = new Dictionary<string, string>
            {
                {":)", "😊"}, {":(", "😢"}, {":D", "😃"}, {";)", "😉"},
                {":P", "😛"}, {":|", "😐"}, {":o", "😮"}, {"<3", "❤️"},
                {":heart:", "❤️"}, {":thumbsup:", "👍"}, {":thumbsdown:", "👎"},
                {":fire:", "🔥"}, {":star:", "⭐"}, {":check:", "✅"},
                {":cross:", "❌"}, {":warning:", "⚠️"}, {":info:", "ℹ️"},
                {":question:", "❓"}, {":exclamation:", "❗"}, {":idea:", "💡"}
            };

            foreach (var emoji in emojis)
            {
                texte = texte.Replace(emoji.Key, emoji.Value);
            }

            return texte;
        }

        /// <summary>
        /// Obtient le nom du fichier joint
        /// </summary>
        private string GetAttachmentName(string attachmentUrl)
        {
            if (string.IsNullOrEmpty(attachmentUrl)) return "";
            return Path.GetFileName(attachmentUrl);
        }

        /// <summary>
        /// Détermine le type de fichier joint
        /// </summary>
        private string GetAttachmentType(string attachmentUrl)
        {
            if (string.IsNullOrEmpty(attachmentUrl)) return "";

            string extension = Path.GetExtension(attachmentUrl).ToLower();
            switch (extension)
            {
                case ".jpg":
                case ".jpeg":
                case ".png":
                case ".gif":
                case ".bmp":
                    return "image";
                case ".pdf":
                    return "pdf";
                case ".doc":
                case ".docx":
                    return "word";
                case ".xls":
                case ".xlsx":
                    return "excel";
                case ".ppt":
                case ".pptx":
                    return "powerpoint";
                case ".zip":
                case ".rar":
                case ".7z":
                    return "archive";
                case ".mp4":
                case ".avi":
                case ".mov":
                    return "video";
                case ".mp3":
                case ".wav":
                    return "audio";
                default:
                    return "file";
            }
        }

        /// <summary>
        /// Valide et sauvegarde un fichier joint
        /// </summary>
        public string SauvegarderFichierJoint(HttpPostedFile fichier, string dossierDestination)
        {
            if (fichier == null || fichier.ContentLength == 0)
                return "";

            // Validation de la taille (max 10MB)
            if (fichier.ContentLength > 10 * 1024 * 1024)
                throw new Exception("Le fichier est trop volumineux (max 10MB)");

            // Extensions autorisées
            string[] extensionsAutorisees = { ".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".zip", ".rar", ".txt" };
            string extension = Path.GetExtension(fichier.FileName).ToLower();

            if (!extensionsAutorisees.Contains(extension))
                throw new Exception("Type de fichier non autorisé");

            // Génération d'un nom unique
            string nomFichier = Guid.NewGuid().ToString() + extension;
            string cheminComplet = Path.Combine(dossierDestination, nomFichier);

            // Création du dossier s'il n'existe pas
            if (!Directory.Exists(dossierDestination))
                Directory.CreateDirectory(dossierDestination);

            // Sauvegarde du fichier
            fichier.SaveAs(cheminComplet);

            return nomFichier;
        }

        /// <summary>
        /// Charge les messages avec pagination
        /// </summary>
        public void ChargerMessagesAvecPagination(Repeater rpt, long conversationId, int page, int messagesParPage = 20)
        {
            using (Connection con = new Connection())
            {
                var messages = from m in con.Messages
                               join mb in con.Membres on m.SenderId equals mb.MembreId
                               where m.ConversationId == conversationId
                               orderby m.DateEnvoi descending
                               select new
                               {
                                   id = m.MessageId,
                                   Contenu = ProcessEmojis(m.Contenu ?? ""),
                                   Expediteur = (mb.Nom ?? "") + " " + (mb.Prenom ?? ""),
                                   Photomembre = mb.PhotoProfil ?? "default-avatar.png",
                                   DateEnvoi = m.DateEnvoi,
                                   name = m.name ?? "",
                                   AttachmentUrl = m.AttachmentUrl ?? "",
                                   AttachmentName = GetAttachmentName(m.AttachmentUrl),
                                   AttachmentType = GetAttachmentType(m.AttachmentUrl),
                                   SenderId = m.SenderId
                               };

                rpt.DataSource = messages.Skip((page - 1) * messagesParPage).Take(messagesParPage).ToList();
                rpt.DataBind();
            }
        }

        /// <summary>
        /// Compte le nombre total de messages dans une conversation
        /// </summary>
        public int CompterMessages(long conversationId)
        {
            using (Connection con = new Connection())
            {
                return con.Messages.Count(m => m.ConversationId == conversationId);
            }
        }

        /// <summary>
        /// Marque tous les messages d'une conversation comme lus pour un utilisateur
        /// </summary>
        public int MarquerTousCommeLus(long conversationId, long userId)
        {
            using (Connection con = new Connection())
            {
                var messagesNonLus = from ms in con.MessageStatus
                                     join m in con.Messages on ms.MessageId equals m.MessageId
                                     where m.ConversationId == conversationId &&
                                           ms.UserId == userId &&
                                           ms.IsRead == 0
                                     select ms;

                foreach (var status in messagesNonLus)
                {
                    status.IsRead = 1;
                    status.ReadAt = DateTime.Now;
                }

                return con.SaveChanges();
            }
        }

        /// <summary>
        /// Obtient les derniers messages non lus pour un utilisateur
        /// </summary>
        public List<object> ObtenirMessagesNonLus(long userId, int limite = 10)
        {
            using (Connection con = new Connection())
            {
                var messagesNonLus = from ms in con.MessageStatus
                                     join m in con.Messages on ms.MessageId equals m.MessageId
                                     join mb in con.Membres on m.SenderId equals mb.MembreId
                                     join c in con.Conversations on m.ConversationId equals c.ConversationId
                                     where ms.UserId == userId && ms.IsRead == 0
                                     orderby m.DateEnvoi descending
                                     select new
                                     {
                                         MessageId = m.MessageId,
                                         ConversationId = m.ConversationId,
                                         Contenu = m.Contenu.Length > 50 ? m.Contenu.Substring(0, 50) + "..." : m.Contenu,
                                         Expediteur = mb.Nom + " " + mb.Prenom,
                                         PhotoExpéditeur = mb.PhotoProfil,
                                         DateEnvoi = m.DateEnvoi,
                                         SujetConversation = c.Sujet
                                     };

                return messagesNonLus.Take(limite).ToList<object>();
            }
        }

    }
}