# 👨‍💻 Guide Développeur - Messagerie LinCom

## 📋 Vue d'ensemble

Ce guide détaille l'architecture technique et l'utilisation des nouvelles fonctionnalités de messagerie de LinCom.

## 🏗️ Architecture

### Structure des Fichiers

```
LinCom/
├── Imp/
│   ├── MessageImp.cs          # Logique métier des messages
│   ├── IMessage.cs            # Interface des messages
│   ├── MembreImp.cs           # Logique métier des membres (recherche)
│   └── IMembre.cs             # Interface des membres
├── Classe/
│   ├── MessageUtility_Class.cs    # Utilitaires pour la messagerie
│   ├── MessageConfig_Class.cs     # Configuration de la messagerie
│   └── Message_Class.cs           # Modèle de données des messages
├── Model/
│   ├── Message.cs             # Entité EF des messages
│   ├── FichierMessage.cs      # Entité EF des fichiers
│   └── MessageStatu.cs        # Entité EF des statuts
├── Content/
│   └── messagerie.css         # Styles CSS de la messagerie
├── Scripts/
│   └── messagerie.js          # JavaScript de la messagerie
├── App_Data/
│   └── MessageConfig.xml      # Configuration XML
├── file/
│   └── messages/              # Dossier des pièces jointes
└── messagerie.aspx(.cs)       # Page principale
```

## 🔧 Classes Principales

### 1. MessageImp.cs

**Méthodes principales :**

```csharp
// Chargement des messages
void ChargerMessages(Repeater rpt, long conversationId, int nombreMessages)

// Recherche dans les messages
void RechercherMessages(Repeater rpt, long conversationId, string motCle, int nombreMessages = 50)

// Envoi de message
int Envoyer(Message_Class messageClass)

// Gestion des pièces jointes
string SauvegarderFichierJoint(HttpPostedFile fichier, string dossierDestination)

// Pagination
void ChargerMessagesAvecPagination(Repeater rpt, long conversationId, int page, int messagesParPage = 20)

// Statuts de lecture
int MarquerCommeLu(long messageId, long userId)
int MarquerTousCommeLus(long conversationId, long userId)
```

### 2. MessageUtility_Class.cs

**Méthodes utilitaires :**

```csharp
// Validation des fichiers
static string ValiderFichier(HttpPostedFile fichier)

// Génération de noms uniques
static string GenererNomFichierUnique(string nomOriginal)

// Conversion des emojis
static string ConvertirEmojis(string texte)

// Nettoyage sécurisé
static string NettoyerMessage(string message)

// Formatage
static string FormaterTailleFichier(long taille)
static string ObtenirIconeFichier(string nomFichier)
```

### 3. MessageConfig_Class.cs

**Configuration centralisée :**

```csharp
// Singleton pour la configuration
MessageConfig_Class.Instance

// Propriétés principales
long MaxFileSize { get; }
int MaxMessageLength { get; }
bool EnableEmojis { get; }
List<string> AllowedFileExtensions { get; }
Dictionary<string, string> GetEmojis()
string GetSystemMessage(string key, params object[] args)
```

## 📝 Utilisation des API

### Envoi d'un Message

```csharp
// Côté serveur (messagerie.aspx.cs)
protected void btnenvoie_ServerClick(object sender, EventArgs e)
{
    try
    {
        // Validation
        if (!ValiderDonnees(txtMessage.Text, lblId.Text))
            return;

        // Nettoyage du message
        string contenu = NettoyerMessage(txtMessage.Text);

        // Gestion des pièces jointes
        string attachmentUrl = "";
        if (fileUpload.HasFile)
        {
            string dossier = Server.MapPath("~/file/messages/");
            attachmentUrl = objmes.SauvegarderFichierJoint(fileUpload.PostedFile, dossier);
        }

        // Création du message
        mess.ConversationId = conversationId;
        mess.SenderId = senderId;
        mess.Contenu = contenu;
        mess.AttachmentUrl = attachmentUrl;

        // Envoi
        int resultat = objmes.Envoyer(mess);
        
        if (resultat > 0)
        {
            ChargerMessages();
            txtMessage.Text = "";
        }
    }
    catch (Exception ex)
    {
        // Gestion d'erreur
    }
}
```

### Recherche de Messages

```csharp
protected void btnRechercheMessage_Click(object sender, EventArgs e)
{
    string motCle = txtRechercheMessage.Text.Trim();
    if (!string.IsNullOrEmpty(motCle))
    {
        objmes.RechercherMessages(rptMessages, conversationId, motCle, 100);
    }
}
```

### Recherche de Contacts

```csharp
protected void txtRechercheContact_TextChanged(object sender, EventArgs e)
{
    string motCle = txtRechercheContact.Text.Trim();
    if (!string.IsNullOrEmpty(motCle))
    {
        objmem.RechercherMembres(listmembre, motCle, "actif");
    }
    else
    {
        objmem.ChargerListview(listmembre, -1, "actif", "");
    }
}
```

## 🎨 Personnalisation CSS

### Variables CSS Disponibles

```css
:root {
    --primary-color: #008374;
    --primary-hover: #006b5e;
    --secondary-color: #f8f9fa;
    --border-color: #ddd;
    --text-color: #333;
    --text-muted: #6c757d;
    --border-radius: 8px;
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
}
```

### Classes CSS Principales

```css
.chat-wrapper          /* Container principal */
.contacts-panel        /* Panneau des contacts */
.chat-panel           /* Panneau de chat */
.message-container    /* Container d'un message */
.message-body         /* Corps du message */
.attachment-container /* Container des pièces jointes */
.emoji-toolbar        /* Barre d'outils emojis */
.message-compose      /* Zone de composition */
```

## 🔧 JavaScript API

### Fonctions Principales

```javascript
// Insertion d'emoji
MessagerieLinCom.insertEmoji('😊');

// Basculer la recherche
MessagerieLinCom.toggleMessageSearch();

// Scroll automatique
MessagerieLinCom.scrollToBottom();

// Notifications
MessagerieLinCom.showNotification('Message envoyé', 'success');

// Envoi de message
MessagerieLinCom.sendMessage();
```

### Configuration JavaScript

```javascript
const MessagerieConfig = {
    autoScrollEnabled: true,
    typingIndicatorTimeout: 3000,
    maxFileSize: 10 * 1024 * 1024,
    allowedFileTypes: ['.jpg', '.png', '.pdf', '.doc'],
    emojiPickerVisible: false
};
```

## 🔒 Sécurité

### Validation des Fichiers

```csharp
// Validation automatique
string erreur = MessageUtility_Class.ValiderFichier(fichier);
if (!string.IsNullOrEmpty(erreur))
{
    // Fichier invalide
    throw new Exception(erreur);
}
```

### Protection XSS

```csharp
// Nettoyage automatique des messages
string messagePropre = MessageUtility_Class.NettoyerMessage(messageUtilisateur);
```

### Extensions Bloquées

Les extensions suivantes sont automatiquement bloquées :
- `.exe`, `.bat`, `.cmd`, `.com`, `.scr`
- `.vbs`, `.js`, `.jar`

## 📊 Performance

### Pagination des Messages

```csharp
// Chargement avec pagination
objmes.ChargerMessagesAvecPagination(rptMessages, conversationId, pageNumber, 20);

// Comptage total
int totalMessages = objmes.CompterMessages(conversationId);
```

### Cache et Optimisation

- Configuration mise en cache (5 minutes)
- Validation côté client pour réduire les aller-retours
- Compression des images automatique
- Lazy loading des contacts

## 🧪 Tests

### Tests Unitaires Recommandés

```csharp
[Test]
public void ValiderFichier_TailleTropGrande_RetourneErreur()
{
    // Arrange
    var fichierMock = CreateMockFile(15 * 1024 * 1024); // 15MB
    
    // Act
    string resultat = MessageUtility_Class.ValiderFichier(fichierMock);
    
    // Assert
    Assert.IsNotEmpty(resultat);
    Assert.Contains("trop volumineux", resultat);
}

[Test]
public void ConvertirEmojis_RaccourcisValides_ConvertitCorrectement()
{
    // Arrange
    string texte = "Salut :) Comment ça va? :D";
    
    // Act
    string resultat = MessageUtility_Class.ConvertirEmojis(texte);
    
    // Assert
    Assert.Contains("😊", resultat);
    Assert.Contains("😃", resultat);
}
```

### Page de Test

Utilisez `test-messagerie.aspx` pour tester :
- Validation des fichiers
- Conversion des emojis
- Nettoyage des messages
- Génération de noms uniques

## 🔄 Migration et Mise à Jour

### Compatibilité

- ✅ Compatible avec les données existantes
- ✅ Pas de modification de schéma requise
- ✅ Fonctionnalités optionnelles

### Déploiement

1. **Copier les nouveaux fichiers**
2. **Créer le dossier** `~/file/messages/`
3. **Configurer les permissions** sur le dossier
4. **Tester** avec `test-messagerie.aspx`
5. **Vérifier** la configuration dans `MessageConfig.xml`

## 🐛 Débogage

### Logs Recommandés

```csharp
// Dans MessageImp.cs
try
{
    // Code métier
}
catch (Exception ex)
{
    System.Diagnostics.Debug.WriteLine($"Erreur messagerie: {ex.Message}");
    // Log dans Event Viewer ou fichier
    throw;
}
```

### Points de Contrôle

1. **Permissions de dossier** : `~/file/messages/`
2. **Taille max upload** : `web.config` vs `MessageConfig.xml`
3. **JavaScript** : Console du navigateur
4. **CSS** : Outils développeur

## 📈 Métriques et Monitoring

### Métriques Recommandées

- Nombre de messages envoyés/jour
- Taille moyenne des pièces jointes
- Temps de réponse des recherches
- Erreurs d'upload de fichiers

### Monitoring

```csharp
// Exemple de logging des métriques
public void LogMessageMetrics(string action, long userId, long? fileSize = null)
{
    var metrics = new
    {
        Action = action,
        UserId = userId,
        FileSize = fileSize,
        Timestamp = DateTime.Now
    };
    
    // Log vers votre système de monitoring
}
```

## 🔮 Extensions Futures

### Fonctionnalités Prévues

1. **SignalR** pour le temps réel
2. **Notifications push**
3. **Appels vidéo/audio**
4. **Partage d'écran**
5. **Traduction automatique**

### Points d'Extension

```csharp
// Interface pour plugins
public interface IMessagePlugin
{
    void OnMessageSent(Message_Class message);
    void OnMessageReceived(Message_Class message);
    string ProcessMessage(string content);
}
```

---

**Développé pour LinCom** - Guide Développeur v1.0
