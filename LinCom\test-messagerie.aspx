<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="test-messagerie.aspx.cs" Inherits="LinCom.test_messagerie" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>Test Messagerie - LinCom</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            color: #008374;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .btn {
            background: #008374;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #006b5e;
        }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <div class="test-container">
            <h1>🧪 Test des Fonctionnalités de Messagerie</h1>
            <p>Cette page permet de tester les nouvelles fonctionnalités de messagerie de LinCom.</p>

            <!-- Test 1: Validation des fichiers -->
            <div class="test-section">
                <div class="test-title">📎 Test de Validation des Fichiers</div>
                <asp:FileUpload ID="fileUploadTest" runat="server" />
                <asp:Button ID="btnTestFile" runat="server" Text="Tester Fichier" CssClass="btn" OnClick="btnTestFile_Click" />
                <asp:Label ID="lblFileResult" runat="server" CssClass="test-result"></asp:Label>
            </div>

            <!-- Test 2: Conversion des emojis -->
            <div class="test-section">
                <div class="test-title">😊 Test de Conversion des Emojis</div>
                <asp:TextBox ID="txtEmojiTest" runat="server" placeholder="Tapez :) :( :D <3 :heart:" Width="300px"></asp:TextBox>
                <asp:Button ID="btnTestEmoji" runat="server" Text="Convertir Emojis" CssClass="btn" OnClick="btnTestEmoji_Click" />
                <div class="test-result info">
                    <strong>Résultat:</strong> <asp:Label ID="lblEmojiResult" runat="server"></asp:Label>
                </div>
            </div>

            <!-- Test 3: Nettoyage des messages -->
            <div class="test-section">
                <div class="test-title">🧹 Test de Nettoyage des Messages</div>
                <asp:TextBox ID="txtCleanTest" runat="server" TextMode="MultiLine" Rows="3" Width="100%" 
                             placeholder="Tapez du texte avec des balises HTML comme <script>alert('test')</script>"></asp:TextBox>
                <asp:Button ID="btnTestClean" runat="server" Text="Nettoyer Message" CssClass="btn" OnClick="btnTestClean_Click" />
                <div class="test-result info">
                    <strong>Résultat:</strong> <asp:Label ID="lblCleanResult" runat="server"></asp:Label>
                </div>
            </div>

            <!-- Test 4: Génération de noms de fichiers -->
            <div class="test-section">
                <div class="test-title">📝 Test de Génération de Noms de Fichiers</div>
                <asp:TextBox ID="txtFileNameTest" runat="server" placeholder="nom du fichier.pdf" Width="300px"></asp:TextBox>
                <asp:Button ID="btnTestFileName" runat="server" Text="Générer Nom Unique" CssClass="btn" OnClick="btnTestFileName_Click" />
                <div class="test-result info">
                    <strong>Résultat:</strong> <asp:Label ID="lblFileNameResult" runat="server"></asp:Label>
                </div>
            </div>

            <!-- Test 5: Icônes de fichiers -->
            <div class="test-section">
                <div class="test-title">🎨 Test des Icônes de Fichiers</div>
                <div style="display: flex; flex-wrap: wrap; gap: 10px;">
                    <span>📄 .txt</span>
                    <span>🖼️ .jpg</span>
                    <span>📕 .pdf</span>
                    <span>📘 .doc</span>
                    <span>📗 .xls</span>
                    <span>📙 .ppt</span>
                    <span>🗜️ .zip</span>
                    <span>🎵 .mp3</span>
                    <span>🎬 .mp4</span>
                </div>
                <asp:TextBox ID="txtIconTest" runat="server" placeholder="fichier.pdf" Width="200px"></asp:TextBox>
                <asp:Button ID="btnTestIcon" runat="server" Text="Obtenir Icône" CssClass="btn" OnClick="btnTestIcon_Click" />
                <div class="test-result info">
                    <strong>Icône:</strong> <asp:Label ID="lblIconResult" runat="server"></asp:Label>
                </div>
            </div>

            <!-- Test 6: Formatage de taille -->
            <div class="test-section">
                <div class="test-title">📏 Test de Formatage de Taille</div>
                <asp:TextBox ID="txtSizeTest" runat="server" placeholder="1048576" Width="200px"></asp:TextBox>
                <span> bytes</span>
                <asp:Button ID="btnTestSize" runat="server" Text="Formater Taille" CssClass="btn" OnClick="btnTestSize_Click" />
                <div class="test-result info">
                    <strong>Taille formatée:</strong> <asp:Label ID="lblSizeResult" runat="server"></asp:Label>
                </div>
            </div>

            <!-- Informations système -->
            <div class="test-section">
                <div class="test-title">ℹ️ Informations Système</div>
                <asp:Label ID="lblSystemInfo" runat="server"></asp:Label>
            </div>

            <!-- Lien vers la messagerie -->
            <div style="text-align: center; margin-top: 30px;">
                <a href="messagerie.aspx" class="btn" style="text-decoration: none;">
                    💬 Aller à la Messagerie
                </a>
            </div>
        </div>
    </form>
</body>
</html>
