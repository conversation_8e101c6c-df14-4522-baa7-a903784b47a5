using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;

namespace LinCom.Classe
{
    /// <summary>
    /// Classe utilitaire pour la gestion des messages et fichiers joints
    /// </summary>
    public class MessageUtility_Class
    {
        /// <summary>
        /// Extensions de fichiers autorisées pour les pièces jointes (utilise la configuration)
        /// </summary>
        public static string[] ExtensionsAutorisees
        {
            get
            {
                try
                {
                    return MessageConfig_Class.Instance.AllowedFileExtensions.ToArray();
                }
                catch
                {
                    // Fallback en cas d'erreur
                    return new string[] {
                        ".jpg", ".jpeg", ".png", ".gif", ".bmp",  // Images
                        ".pdf",                                    // PDF
                        ".doc", ".docx",                          // Word
                        ".xls", ".xlsx",                          // Excel
                        ".ppt", ".pptx",                          // PowerPoint
                        ".txt", ".rtf",                           // Texte
                        ".zip", ".rar", ".7z",                    // Archives
                        ".mp3", ".wav",                           // Audio
                        ".mp4", ".avi", ".mov"                    // Vidéo
                    };
                }
            }
        }

        /// <summary>
        /// Taille maximale autorisée pour les fichiers (utilise la configuration)
        /// </summary>
        public static long TailleMaxFichier
        {
            get
            {
                try
                {
                    return MessageConfig_Class.Instance.MaxFileSize;
                }
                catch
                {
                    return 10 * 1024 * 1024; // 10MB par défaut
                }
            }
        }

        /// <summary>
        /// Valide un fichier uploadé
        /// </summary>
        /// <param name="fichier">Le fichier à valider</param>
        /// <returns>Message d'erreur ou string.Empty si valide</returns>
        public static string ValiderFichier(HttpPostedFile fichier)
        {
            if (fichier == null || fichier.ContentLength == 0)
                return "Aucun fichier sélectionné";

            // Vérifier la taille
            if (fichier.ContentLength > TailleMaxFichier)
                return $"Le fichier est trop volumineux (maximum {TailleMaxFichier / (1024 * 1024)}MB)";

            // Vérifier l'extension
            string extension = Path.GetExtension(fichier.FileName).ToLower();
            if (!ExtensionsAutorisees.Contains(extension))
                return $"Type de fichier non autorisé. Extensions autorisées: {string.Join(", ", ExtensionsAutorisees)}";

            // Vérifier le nom du fichier
            if (string.IsNullOrWhiteSpace(fichier.FileName))
                return "Nom de fichier invalide";

            return string.Empty; // Fichier valide
        }

        /// <summary>
        /// Génère un nom de fichier unique
        /// </summary>
        /// <param name="nomOriginal">Nom original du fichier</param>
        /// <returns>Nom de fichier unique</returns>
        public static string GenererNomFichierUnique(string nomOriginal)
        {
            string extension = Path.GetExtension(nomOriginal);
            string nomSansExtension = Path.GetFileNameWithoutExtension(nomOriginal);
            
            // Nettoyer le nom de fichier
            nomSansExtension = System.Text.RegularExpressions.Regex.Replace(nomSansExtension, @"[^a-zA-Z0-9_-]", "_");
            
            // Générer un nom unique
            string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            string guid = Guid.NewGuid().ToString("N").Substring(0, 8);
            
            return $"{nomSansExtension}_{timestamp}_{guid}{extension}";
        }

        /// <summary>
        /// Obtient l'icône appropriée pour un type de fichier
        /// </summary>
        /// <param name="nomFichier">Nom du fichier</param>
        /// <returns>Emoji ou icône représentant le type de fichier</returns>
        public static string ObtenirIconeFichier(string nomFichier)
        {
            if (string.IsNullOrEmpty(nomFichier))
                return "📄";

            string extension = Path.GetExtension(nomFichier).ToLower();
            
            switch (extension)
            {
                case ".jpg":
                case ".jpeg":
                case ".png":
                case ".gif":
                case ".bmp":
                    return "🖼️";
                case ".pdf":
                    return "📕";
                case ".doc":
                case ".docx":
                    return "📘";
                case ".xls":
                case ".xlsx":
                    return "📗";
                case ".ppt":
                case ".pptx":
                    return "📙";
                case ".txt":
                case ".rtf":
                    return "📝";
                case ".zip":
                case ".rar":
                case ".7z":
                    return "🗜️";
                case ".mp3":
                case ".wav":
                    return "🎵";
                case ".mp4":
                case ".avi":
                case ".mov":
                    return "🎬";
                default:
                    return "📄";
            }
        }

        /// <summary>
        /// Formate la taille d'un fichier en format lisible
        /// </summary>
        /// <param name="taille">Taille en bytes</param>
        /// <returns>Taille formatée (ex: "1.5 MB")</returns>
        public static string FormaterTailleFichier(long taille)
        {
            string[] unites = { "B", "KB", "MB", "GB" };
            double tailleDouble = taille;
            int uniteIndex = 0;

            while (tailleDouble >= 1024 && uniteIndex < unites.Length - 1)
            {
                tailleDouble /= 1024;
                uniteIndex++;
            }

            return $"{tailleDouble:F1} {unites[uniteIndex]}";
        }

        /// <summary>
        /// Nettoie le contenu d'un message pour éviter les injections
        /// </summary>
        /// <param name="message">Message à nettoyer</param>
        /// <returns>Message nettoyé</returns>
        public static string NettoyerMessage(string message)
        {
            if (string.IsNullOrEmpty(message))
                return string.Empty;

            // Supprimer les balises script
            message = System.Text.RegularExpressions.Regex.Replace(
                message, 
                @"<script[^>]*>.*?</script>", 
                "", 
                System.Text.RegularExpressions.RegexOptions.IgnoreCase | System.Text.RegularExpressions.RegexOptions.Singleline
            );

            // Supprimer les autres balises potentiellement dangereuses
            string[] balisesInterdites = { "iframe", "object", "embed", "form", "input", "button" };
            foreach (string balise in balisesInterdites)
            {
                message = System.Text.RegularExpressions.Regex.Replace(
                    message,
                    $@"<{balise}[^>]*>.*?</{balise}>",
                    "",
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase | System.Text.RegularExpressions.RegexOptions.Singleline
                );
            }

            // Encoder les caractères HTML dangereux
            message = HttpUtility.HtmlEncode(message);

            // Permettre certaines balises sûres (optionnel)
            message = message.Replace("&lt;b&gt;", "<b>").Replace("&lt;/b&gt;", "</b>");
            message = message.Replace("&lt;i&gt;", "<i>").Replace("&lt;/i&gt;", "</i>");
            message = message.Replace("&lt;u&gt;", "<u>").Replace("&lt;/u&gt;", "</u>");

            return message.Trim();
        }

        /// <summary>
        /// Convertit les raccourcis texte en emojis (utilise la configuration)
        /// </summary>
        /// <param name="texte">Texte contenant des raccourcis</param>
        /// <returns>Texte avec emojis</returns>
        public static string ConvertirEmojis(string texte)
        {
            if (string.IsNullOrEmpty(texte))
                return texte;

            try
            {
                // Utiliser la configuration pour obtenir les emojis
                if (MessageConfig_Class.Instance.EnableEmojis)
                {
                    var emojis = MessageConfig_Class.Instance.GetEmojis();
                    foreach (var emoji in emojis)
                    {
                        texte = texte.Replace(emoji.Key, emoji.Value);
                    }
                }
            }
            catch
            {
                // Fallback avec emojis par défaut
                var emojis = new Dictionary<string, string>
                {
                    {":)", "😊"}, {":(", "😢"}, {":D", "😃"}, {";)", "😉"},
                    {":P", "😛"}, {":|", "😐"}, {":o", "😮"}, {"<3", "❤️"},
                    {":heart:", "❤️"}, {":thumbsup:", "👍"}, {":fire:", "🔥"}
                };

                foreach (var emoji in emojis)
                {
                    texte = texte.Replace(emoji.Key, emoji.Value);
                }
            }

            return texte;
        }

        /// <summary>
        /// Crée le dossier de destination s'il n'existe pas
        /// </summary>
        /// <param name="chemin">Chemin du dossier</param>
        public static void CreerDossierSiNecessaire(string chemin)
        {
            if (!Directory.Exists(chemin))
            {
                Directory.CreateDirectory(chemin);
            }
        }

        /// <summary>
        /// Supprime un fichier de manière sécurisée
        /// </summary>
        /// <param name="cheminFichier">Chemin complet du fichier</param>
        /// <returns>True si supprimé avec succès</returns>
        public static bool SupprimerFichier(string cheminFichier)
        {
            try
            {
                if (File.Exists(cheminFichier))
                {
                    File.Delete(cheminFichier);
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
    }
}
