/* ===================================
   STYLES POUR LA MESSAGERIE LINCOM
   =================================== */

/* Variables CSS pour la cohérence */
:root {
    --primary-color: #008374;
    --primary-hover: #006b5e;
    --secondary-color: #f8f9fa;
    --border-color: #ddd;
    --text-color: #333;
    --text-muted: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --border-radius: 8px;
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Layout principal de la messagerie */
.chat-wrapper {
    display: flex;
    height: 80vh;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    background: white;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* ===================================
   PANNEAU DES CONTACTS
   =================================== */
.contacts-panel {
    width: 300px;
    background: var(--secondary-color);
    border-right: 1px solid var(--border-color);
    overflow-y: auto;
    flex-shrink: 0;
}

.contacts-header {
    background: var(--primary-color);
    color: white;
    padding: 15px;
    font-weight: bold;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.contacts-search {
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    gap: 8px;
}

.search-input {
    flex: 1;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    outline: none;
    font-size: 14px;
    transition: border-color 0.3s;
}

.search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 131, 116, 0.1);
}

.search-btn {
    padding: 10px 15px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.search-btn:hover {
    background: var(--primary-hover);
}

/* Items de contact */
.contact-item {
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    transition: background-color 0.3s;
    border-bottom: 1px solid #eee;
    text-decoration: none;
    color: var(--text-color);
    position: relative;
}

.contact-item:hover {
    background: #e0f7f5;
    text-decoration: none;
    color: var(--text-color);
}

.contact-item.active {
    background: var(--primary-color);
    color: white;
}

.contact-item img {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.contact-name {
    font-weight: 500;
    font-size: 14px;
}

.contact-status {
    font-size: 12px;
    color: var(--text-muted);
}

.online-indicator {
    width: 10px;
    height: 10px;
    background: var(--success-color);
    border-radius: 50%;
    position: absolute;
    bottom: 18px;
    left: 50px;
    border: 2px solid white;
}

/* ===================================
   PANNEAU DE CHAT
   =================================== */
.chat-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
}

.chat-header {
    background: white;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    font-weight: bold;
    font-size: 16px;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 10px;
}

.chat-body {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #fafafa;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* ===================================
   MESSAGES
   =================================== */
.message-container {
    display: flex;
    flex-direction: column;
    max-width: 70%;
    margin-bottom: 15px;
}

.message-container.sent {
    align-self: flex-end;
    align-items: flex-end;
}

.message-container.received {
    align-self: flex-start;
    align-items: flex-start;
}

.message-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 5px;
    font-size: 12px;
    color: var(--text-muted);
}

.message-header .avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    object-fit: cover;
}

.message-header .date {
    font-size: 11px;
    color: var(--text-muted);
}

.message-body {
    padding: 12px 16px;
    border-radius: 18px;
    max-width: 100%;
    word-wrap: break-word;
    line-height: 1.4;
    font-size: 14px;
}

.message-container.sent .message-body {
    background: var(--primary-color);
    color: white;
    border-bottom-right-radius: 6px;
}

.message-container.received .message-body {
    background: white;
    color: var(--text-color);
    border: 1px solid #e1e8ed;
    border-bottom-left-radius: 6px;
}

.message-body p {
    margin: 0;
    line-height: 1.4;
}

/* ===================================
   PIÈCES JOINTES
   =================================== */
.attachment-container {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 12px;
    border-radius: 8px;
    margin-top: 8px;
    border-left: 3px solid rgba(255, 255, 255, 0.3);
}

.message-container.received .attachment-container {
    background: var(--secondary-color);
    border-left-color: var(--primary-color);
}

.attachment-link {
    color: inherit !important;
    text-decoration: none;
    font-weight: 500;
    font-size: 13px;
}

.attachment-link:hover {
    text-decoration: underline;
}

.attachment-info {
    font-size: 11px;
    opacity: 0.7;
    font-style: italic;
}

/* ===================================
   ZONE DE COMPOSITION
   =================================== */
.chat-footer {
    border-top: 1px solid var(--border-color);
    background: white;
}

.message-search {
    padding: 12px 20px;
    background: var(--secondary-color);
    border-bottom: 1px solid var(--border-color);
    display: none;
    gap: 10px;
    align-items: center;
}

.close-search-btn {
    background: var(--danger-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 10px;
    cursor: pointer;
    font-size: 12px;
}

.emoji-toolbar {
    display: flex;
    gap: 5px;
    padding: 12px 20px;
    background: var(--secondary-color);
    border-bottom: 1px solid var(--border-color);
    flex-wrap: wrap;
    align-items: center;
}

.emoji-btn {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    padding: 6px;
    border-radius: 6px;
    transition: background-color 0.2s;
    line-height: 1;
}

.emoji-btn:hover {
    background: #e9ecef;
}

.search-toggle-btn {
    background: var(--text-muted);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 10px;
    cursor: pointer;
    font-size: 12px;
    margin-left: auto;
}

.search-toggle-btn:hover {
    background: #5a6268;
}

.message-compose {
    padding: 20px;
    display: flex;
    gap: 15px;
    align-items: flex-end;
}

.message-input-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.message-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--border-color);
    border-radius: 20px;
    resize: none;
    min-height: 20px;
    max-height: 120px;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.4;
    outline: none;
    transition: border-color 0.3s;
}

.message-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 131, 116, 0.1);
}

.attachment-section {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 12px;
}

.file-input {
    display: none;
}

.file-label {
    background: var(--text-muted);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    gap: 5px;
}

.file-label:hover {
    background: #5a6268;
}

.file-name {
    color: var(--text-muted);
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.send-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 20px;
    cursor: pointer;
    font-weight: 500;
    font-size: 14px;
    transition: background-color 0.3s;
    min-width: 80px;
}

.send-btn:hover {
    background: var(--primary-hover);
}

.send-btn:disabled {
    background: var(--text-muted);
    cursor: not-allowed;
}

/* ===================================
   INDICATEURS ET ANIMATIONS
   =================================== */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    font-style: italic;
    color: var(--text-muted);
    font-size: 13px;
}

.typing-dots {
    display: flex;
    gap: 3px;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    background: var(--text-muted);
    border-radius: 50%;
    animation: typing 1.4s infinite;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.4;
    }
    30% {
        transform: translateY(-8px);
        opacity: 1;
    }
}

/* ===================================
   RESPONSIVE DESIGN
   =================================== */
@media (max-width: 768px) {
    .chat-wrapper {
        height: 90vh;
        flex-direction: column;
    }
    
    .contacts-panel {
        width: 100%;
        height: 200px;
        border-right: none;
        border-bottom: 1px solid var(--border-color);
    }
    
    .message-container {
        max-width: 85%;
    }
    
    .message-compose {
        padding: 15px;
        gap: 10px;
    }
    
    .emoji-toolbar {
        padding: 10px 15px;
    }
    
    .send-btn {
        padding: 10px 20px;
        min-width: 70px;
    }
}

@media (max-width: 480px) {
    .message-container {
        max-width: 95%;
    }
    
    .message-body {
        padding: 10px 14px;
        font-size: 13px;
    }
    
    .emoji-btn {
        font-size: 18px;
        padding: 4px;
    }
}

/* ===================================
   ÉTATS ET FEEDBACK
   =================================== */
.message-status {
    font-size: 11px;
    color: var(--text-muted);
    margin-top: 2px;
    text-align: right;
}

.message-status.read {
    color: var(--primary-color);
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 10px;
    border-radius: 6px;
    margin: 10px 20px;
    border: 1px solid #f5c6cb;
}

.success-message {
    background: #d4edda;
    color: #155724;
    padding: 10px;
    border-radius: 6px;
    margin: 10px 20px;
    border: 1px solid #c3e6cb;
}

/* ===================================
   SCROLLBAR PERSONNALISÉE
   =================================== */
.chat-body::-webkit-scrollbar,
.contacts-panel::-webkit-scrollbar {
    width: 6px;
}

.chat-body::-webkit-scrollbar-track,
.contacts-panel::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.chat-body::-webkit-scrollbar-thumb,
.contacts-panel::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-body::-webkit-scrollbar-thumb:hover,
.contacts-panel::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
