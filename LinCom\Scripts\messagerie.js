/**
 * MESSAGERIE LINCOM - FONCTIONNALITÉS JAVASCRIPT
 * ===============================================
 */

// Configuration globale
const MessagerieConfig = {
    autoScrollEnabled: true,
    typingIndicatorTimeout: 3000,
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedFileTypes: ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx', '.txt'],
    emojiPickerVisible: false
};

// Variables globales
let typingTimer;
let lastMessageCount = 0;

/**
 * Initialisation de la messagerie
 */
function initializeMessagerie() {
    console.log('🚀 Initialisation de la messagerie LinCom');
    
    // Initialiser les événements
    setupEventListeners();
    
    // Scroll automatique vers le bas
    scrollToBottom();
    
    // Démarrer la surveillance des nouveaux messages
    startMessagePolling();
    
    // Initialiser le redimensionnement automatique du textarea
    setupAutoResize();
    
    console.log('✅ Messagerie initialisée avec succès');
}

/**
 * Configuration des écouteurs d'événements
 */
function setupEventListeners() {
    // Envoi de message avec Enter
    const messageInput = document.getElementById(getClientId('txtMessage'));
    if (messageInput) {
        messageInput.addEventListener('keydown', handleMessageInputKeydown);
        messageInput.addEventListener('input', handleTypingIndicator);
    }

    // Gestion du fichier sélectionné
    const fileInput = document.getElementById(getClientId('fileUpload'));
    if (fileInput) {
        fileInput.addEventListener('change', handleFileSelection);
    }

    // Recherche de contacts
    const searchInput = document.getElementById(getClientId('txtRechercheContact'));
    if (searchInput) {
        searchInput.addEventListener('input', debounce(handleContactSearch, 300));
    }

    // Gestion du redimensionnement de la fenêtre
    window.addEventListener('resize', handleWindowResize);
    
    // Prévenir la soumission accidentelle du formulaire
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            return false;
        });
    }
}

/**
 * Gestion de la saisie dans le champ message
 */
function handleMessageInputKeydown(e) {
    if (e.key === 'Enter' && !e.shiftKey && !e.ctrlKey) {
        e.preventDefault();
        sendMessage();
    }
}

/**
 * Envoi d'un message
 */
function sendMessage() {
    const messageInput = document.getElementById(getClientId('txtMessage'));
    const sendButton = document.getElementById(getClientId('btnenvoie'));
    
    if (!messageInput || !sendButton) return;
    
    const message = messageInput.value.trim();
    
    // Validation côté client
    if (!message && !hasFileAttachment()) {
        showNotification('Veuillez saisir un message ou sélectionner un fichier', 'warning');
        return;
    }
    
    if (message.length > 1000) {
        showNotification('Le message est trop long (maximum 1000 caractères)', 'error');
        return;
    }
    
    // Désactiver le bouton d'envoi temporairement
    sendButton.disabled = true;
    sendButton.textContent = 'Envoi...';
    
    // Déclencher l'événement côté serveur
    setTimeout(() => {
        sendButton.disabled = false;
        sendButton.textContent = 'Envoyer';
        sendButton.click();
    }, 100);
}

/**
 * Vérification de la présence d'un fichier joint
 */
function hasFileAttachment() {
    const fileInput = document.getElementById(getClientId('fileUpload'));
    return fileInput && fileInput.files.length > 0;
}

/**
 * Gestion de la sélection de fichier
 */
function handleFileSelection(e) {
    const file = e.target.files[0];
    const fileNameLabel = document.getElementById(getClientId('lblFileName'));
    
    if (!file) {
        if (fileNameLabel) fileNameLabel.textContent = '';
        return;
    }
    
    // Validation du fichier
    const validation = validateFile(file);
    if (!validation.isValid) {
        showNotification(validation.message, 'error');
        e.target.value = ''; // Réinitialiser le champ
        if (fileNameLabel) fileNameLabel.textContent = '';
        return;
    }
    
    // Afficher le nom du fichier
    if (fileNameLabel) {
        const fileIcon = getFileIcon(file.name);
        const fileSize = formatFileSize(file.size);
        fileNameLabel.innerHTML = `${fileIcon} ${file.name} (${fileSize})`;
    }
    
    showNotification(`Fichier sélectionné: ${file.name}`, 'success');
}

/**
 * Validation d'un fichier
 */
function validateFile(file) {
    // Vérifier la taille
    if (file.size > MessagerieConfig.maxFileSize) {
        return {
            isValid: false,
            message: `Le fichier est trop volumineux (maximum ${formatFileSize(MessagerieConfig.maxFileSize)})`
        };
    }
    
    // Vérifier l'extension
    const extension = '.' + file.name.split('.').pop().toLowerCase();
    if (!MessagerieConfig.allowedFileTypes.includes(extension)) {
        return {
            isValid: false,
            message: `Type de fichier non autorisé. Extensions autorisées: ${MessagerieConfig.allowedFileTypes.join(', ')}`
        };
    }
    
    return { isValid: true };
}

/**
 * Obtenir l'icône pour un type de fichier
 */
function getFileIcon(fileName) {
    const extension = '.' + fileName.split('.').pop().toLowerCase();
    const icons = {
        '.jpg': '🖼️', '.jpeg': '🖼️', '.png': '🖼️', '.gif': '🖼️',
        '.pdf': '📕',
        '.doc': '📘', '.docx': '📘',
        '.xls': '📗', '.xlsx': '📗',
        '.ppt': '📙', '.pptx': '📙',
        '.txt': '📝',
        '.zip': '🗜️', '.rar': '🗜️',
        '.mp3': '🎵', '.wav': '🎵',
        '.mp4': '🎬', '.avi': '🎬'
    };
    return icons[extension] || '📄';
}

/**
 * Formatage de la taille de fichier
 */
function formatFileSize(bytes) {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`;
}

/**
 * Insertion d'emoji dans le champ de message
 */
function insertEmoji(emoji) {
    const messageInput = document.getElementById(getClientId('txtMessage'));
    if (!messageInput) return;
    
    const cursorPos = messageInput.selectionStart;
    const textBefore = messageInput.value.substring(0, cursorPos);
    const textAfter = messageInput.value.substring(messageInput.selectionEnd);
    
    messageInput.value = textBefore + emoji + textAfter;
    messageInput.focus();
    
    // Repositionner le curseur
    const newCursorPos = cursorPos + emoji.length;
    messageInput.setSelectionRange(newCursorPos, newCursorPos);
    
    // Déclencher l'événement input pour le redimensionnement automatique
    messageInput.dispatchEvent(new Event('input'));
}

/**
 * Basculer l'affichage de la recherche de messages
 */
function toggleMessageSearch() {
    const searchDiv = document.querySelector('.message-search');
    const searchInput = document.getElementById(getClientId('txtRechercheMessage'));
    
    if (!searchDiv) return;
    
    if (searchDiv.style.display === 'none' || searchDiv.style.display === '') {
        searchDiv.style.display = 'flex';
        if (searchInput) {
            searchInput.focus();
        }
    } else {
        searchDiv.style.display = 'none';
        if (searchInput) {
            searchInput.value = '';
        }
    }
}

/**
 * Scroll automatique vers le bas
 */
function scrollToBottom() {
    if (!MessagerieConfig.autoScrollEnabled) return;
    
    const chatBody = document.querySelector('.chat-body');
    if (chatBody) {
        chatBody.scrollTop = chatBody.scrollHeight;
    }
}

/**
 * Surveillance des nouveaux messages
 */
function startMessagePolling() {
    // Observer les changements dans la zone de messages
    const chatBody = document.querySelector('.chat-body');
    if (!chatBody) return;
    
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                // Nouveau message détecté
                scrollToBottom();
                
                // Animation d'apparition pour les nouveaux messages
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === Node.ELEMENT_NODE && node.classList.contains('message-container')) {
                        node.style.opacity = '0';
                        node.style.transform = 'translateY(20px)';
                        
                        setTimeout(() => {
                            node.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                            node.style.opacity = '1';
                            node.style.transform = 'translateY(0)';
                        }, 50);
                    }
                });
            }
        });
    });
    
    observer.observe(chatBody, { childList: true, subtree: true });
}

/**
 * Configuration du redimensionnement automatique du textarea
 */
function setupAutoResize() {
    const messageInput = document.getElementById(getClientId('txtMessage'));
    if (!messageInput) return;
    
    messageInput.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = Math.min(this.scrollHeight, 120) + 'px';
    });
}

/**
 * Gestion de l'indicateur de frappe
 */
function handleTypingIndicator() {
    // Effacer le timer précédent
    clearTimeout(typingTimer);
    
    // Afficher l'indicateur de frappe (à implémenter côté serveur)
    // showTypingIndicator();
    
    // Masquer l'indicateur après un délai
    typingTimer = setTimeout(() => {
        // hideTypingIndicator();
    }, MessagerieConfig.typingIndicatorTimeout);
}

/**
 * Recherche de contacts avec debounce
 */
function handleContactSearch(e) {
    const searchTerm = e.target.value.trim();
    
    if (searchTerm.length >= 2) {
        // Déclencher la recherche côté serveur
        const searchButton = document.getElementById(getClientId('btnRechercheContact'));
        if (searchButton) {
            searchButton.click();
        }
    } else if (searchTerm.length === 0) {
        // Recharger tous les contacts
        const searchButton = document.getElementById(getClientId('btnRechercheContact'));
        if (searchButton) {
            searchButton.click();
        }
    }
}

/**
 * Gestion du redimensionnement de la fenêtre
 */
function handleWindowResize() {
    // Ajuster la hauteur du chat sur mobile
    if (window.innerWidth <= 768) {
        const chatWrapper = document.querySelector('.chat-wrapper');
        if (chatWrapper) {
            chatWrapper.style.height = '90vh';
        }
    }
}

/**
 * Affichage de notifications
 */
function showNotification(message, type = 'info') {
    // Créer l'élément de notification
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <span>${message}</span>
        <button onclick="this.parentElement.remove()" style="background:none;border:none;color:inherit;float:right;cursor:pointer;">&times;</button>
    `;
    
    // Styles pour la notification
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 6px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        max-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        animation: slideIn 0.3s ease;
    `;
    
    // Couleurs selon le type
    const colors = {
        success: '#28a745',
        error: '#dc3545',
        warning: '#ffc107',
        info: '#17a2b8'
    };
    notification.style.backgroundColor = colors[type] || colors.info;
    
    // Ajouter au DOM
    document.body.appendChild(notification);
    
    // Supprimer automatiquement après 5 secondes
    setTimeout(() => {
        if (notification.parentElement) {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => notification.remove(), 300);
        }
    }, 5000);
}

/**
 * Utilitaires
 */

// Fonction debounce pour limiter les appels
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Obtenir l'ID client d'un contrôle ASP.NET
function getClientId(controlId) {
    // Cette fonction doit être adaptée selon la structure de votre page
    const element = document.querySelector(`[id$="${controlId}"]`);
    return element ? element.id : controlId;
}

// Ajouter les styles CSS pour les animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    .notification {
        transition: all 0.3s ease;
    }
`;
document.head.appendChild(style);

/**
 * Initialisation au chargement de la page
 */
document.addEventListener('DOMContentLoaded', function() {
    initializeMessagerie();
});

// Initialisation alternative pour ASP.NET
if (typeof Sys !== 'undefined') {
    Sys.Application.add_load(function() {
        initializeMessagerie();
    });
}

// Export pour utilisation globale
window.MessagerieLinCom = {
    insertEmoji,
    toggleMessageSearch,
    scrollToBottom,
    showNotification,
    sendMessage
};
