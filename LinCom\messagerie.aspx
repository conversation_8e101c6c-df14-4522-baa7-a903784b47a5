﻿<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="messagerie.aspx.cs" Inherits="LinCom.messagerie" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <link href="Content/messagerie.css" rel="stylesheet" type="text/css" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Page Title -->
    <div class="page-title">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="home.aspx">Home</a></li>
                    <li><a href="#">Espace FNUAP</a></li>
                    <li><a href="#">Bibliothèque Digitale de FNUAP</a></li>
                    <li class="current"><a href="ressources-document.aspx">Documents officiels</a></li>
                </ol>
            </div>
        </nav>
    </div>
    <!-- End Page Title -->
    <main class="main">
        <asp:HiddenField ID="hdnConversationId" runat="server" />
<asp:HiddenField ID="hdnIsGroup" runat="server" />
<asp:HiddenField ID="hdnCurrentUserId" runat="server" />

        <div class="container py-4">
            <h2 class="mb-4">💬 Espace Messagerie</h2>

            <div class="chat-wrapper">

                <!-- Sidebar -->
                <div class="contacts-panel">
                    <div class="contacts-header">👥 Contacts</div>
                    <%-- Recherche de contacts temporairement désactivée
                    <div class="contacts-search">
                        <asp:TextBox ID="txtRechercheContact" runat="server" placeholder="Rechercher un contact..."
                                     CssClass="search-input" AutoPostBack="true" OnTextChanged="txtRechercheContact_TextChanged"></asp:TextBox>
                        <asp:Button ID="btnRechercheContact" runat="server" Text="🔍" CssClass="search-btn" OnClick="btnRechercheContact_Click" />
                    </div>
                    --%>
                    --%>
                    <asp:ListView ID="listmembre" runat="server" OnItemCommand="listmembre_ItemCommand">
                        <EmptyDataTemplate>Aucune Donnée</EmptyDataTemplate>
                        <ItemTemplate>
                            <asp:LinkButton ID="btnSelectMembre" runat="server"
                                CommandName="viewmem"
                                CommandArgument='<%# Eval("id") %>'
                                CssClass="contact-item">
        <img src='<%# HttpUtility.HtmlEncode(string.Concat("../file/membr/", Eval("PhotoProfil"))) %>' alt="Nom du Membre" />
        <div>
            <div class="contact-name"><%# HttpUtility.HtmlEncode(Eval("Membre")) %></div>
        </div>
                            </asp:LinkButton>
                                <!-- 🔽 Champ caché pour dire si c’est un groupe -->
 
                        </ItemTemplate>
                    </asp:ListView>

                </div>


                <!-- Main Chat Area -->
                <div class="chat-panel">

                    <div class="chat-header">
                        <asp:Label ID="lblHeader" runat="server" Text="Sélectionnez un contact pour discuter"></asp:Label>
                        <asp:Label ID="lblId" Visible="false" runat="server" Text="0"></asp:Label>
                    </div>

                    <div class="chat-body">
                      <asp:Repeater ID="rptMessages" runat="server">
    <ItemTemplate>
        <div class='message-container <%# Convert.ToInt64(Eval("SenderId")) == GetCurrentUserId() ? "sent" : "received" %>'>
            <div class="message-header">
                <img class="avatar" src='<%# ResolveUrl("~/file/membr/") + (string.IsNullOrEmpty(Eval("Photomembre").ToString()) ? "default-avatar.png" : Eval("Photomembre")) %>'
                     alt="Photo" onerror="this.src='<%# ResolveUrl(\"~/file/membr/default-avatar.png\") %>'" />
                <strong><%# Eval("Expediteur") %></strong>
                <span class="date"><%# Eval("DateEnvoi", "{0:dd MMM yyyy HH:mm}") %></span>
            </div>

            <div class="message-body">
                <p><%# Eval("Contenu") %></p>

                <%-- Si le message contient une pièce jointe --%>
                <asp:Panel runat="server" Visible='<%# !string.IsNullOrEmpty(Eval("AttachmentUrl").ToString()) %>'>
                    <div class="attachment-container">
                        <%# GetAttachmentIcon(Eval("AttachmentType").ToString()) %>
                        <a href='<%# ResolveUrl("~/file/messages/") + Eval("AttachmentUrl") %>' target="_blank" class="attachment-link">
                            <%# Eval("AttachmentName") %>
                        </a>
                        <span class="attachment-info">
                            (<%# Eval("AttachmentType") %>)
                        </span>
                    </div>
                </asp:Panel>
            </div>
        </div>
    </ItemTemplate>
</asp:Repeater>

                    </div>

                    <div class="chat-footer">
                        <%-- Zone de recherche dans les messages temporairement désactivée
                        <div class="message-search" style="display: none;">
                            <asp:TextBox ID="txtRechercheMessage" runat="server" placeholder="Rechercher dans les messages..."
                                         CssClass="search-input"></asp:TextBox>
                            <asp:Button ID="btnRechercheMessage" runat="server" Text="🔍" CssClass="search-btn" OnClick="btnRechercheMessage_Click" />
                            <button type="button" onclick="toggleMessageSearch()" class="close-search-btn">✖</button>
                        </div>
                        --%>

                        <!-- Zone de composition du message -->
                        <div class="message-compose">
                            <div class="emoji-toolbar">
                                <button type="button" onclick="insertEmoji('😊')" class="emoji-btn">😊</button>
                                <button type="button" onclick="insertEmoji('😢')" class="emoji-btn">😢</button>
                                <button type="button" onclick="insertEmoji('😃')" class="emoji-btn">😃</button>
                                <button type="button" onclick="insertEmoji('😉')" class="emoji-btn">😉</button>
                                <button type="button" onclick="insertEmoji('❤️')" class="emoji-btn">❤️</button>
                                <button type="button" onclick="insertEmoji('👍')" class="emoji-btn">👍</button>
                                <button type="button" onclick="insertEmoji('👎')" class="emoji-btn">👎</button>
                                <button type="button" onclick="insertEmoji('🔥')" class="emoji-btn">🔥</button>
                                <button type="button" onclick="toggleMessageSearch()" class="search-toggle-btn" title="Rechercher dans les messages">🔍</button>
                            </div>

                            <div class="message-input-container">
                                <textarea rows="2" runat="server" id="txtMessage" class="message-input"
                                          placeholder="Écrivez votre message... (utilisez :) :( :D pour les emojis)"></textarea>

                                <%-- Section pièces jointes temporairement désactivée
                                <div class="attachment-section">
                                    <asp:FileUpload ID="fileUpload" runat="server" CssClass="file-input" />
                                    <label for="<%= fileUpload.ClientID %>" class="file-label">📎</label>
                                    <asp:Label ID="lblFileName" runat="server" CssClass="file-name"></asp:Label>
                                </div>
                                --%>
                            </div>

                            <button type="button" runat="server" id="btnenvoie" class="send-btn" onserverclick="btnenvoie_ServerClick">Envoyer</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </main>

    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
        }

        .chat-wrapper {
            display: flex;
            height: 80vh;
            border-radius: 12px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            overflow: hidden;
            background: #fff;
        }

        .contacts-panel {
            width: 280px;
            background: #f4f4f4;
            border-right: 1px solid #ddd;
            overflow-y: auto;
        }

        .contacts-header {
            background: #008374;
            color: #fff;
            padding: 15px;
            font-weight: bold;
        }

        .contact-item {
            padding: 12px;
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }

            .contact-item:hover {
                background: #e0f7f5;
            }

            .contact-item img {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                object-fit: cover;
            }

        .contact-name {
            font-weight: 500;
        }

        .chat-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #fafafa;
        }

        .chat-header {
            background: #fff;
            padding: 15px;
            border-bottom: 1px solid #eee;
            font-weight: bold;
        }

        .chat-body {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .bubble {
            max-width: 60%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            position: relative;
        }

            .bubble.received {
                background: #eee;
                align-self: flex-start;
            }

            .bubble.sent {
                background: #008374;
                color: white;
                align-self: flex-end;
            }

        .chat-footer {
            padding: 12px 15px;
            background: #fff;
            border-top: 1px solid #eee;
            display: flex;
            gap: 10px;
        }

            .chat-footer textarea {
                flex: 1;
                border-radius: 10px;
                padding: 10px;
                border: 1px solid #ccc;
                resize: none;
            }

            .chat-footer button {
                background: #008374;
                color: #fff;
                border: none;
                padding: 10px 20px;
                border-radius: 10px;
                font-weight: bold;
            }

        .online-dot {
            height: 10px;
            width: 10px;
            background-color: #28a745;
            border-radius: 50%;
            display: inline-block;
        }

        .contacts-search {
            padding: 10px;
            border-bottom: 1px solid #ddd;
            display: flex;
            gap: 5px;
        }

        .search-input {
            flex: 1;
            padding: 8px;
            border-radius: 8px;
            border: 1px solid #ccc;
            outline: none;
        }

        .search-btn {
            padding: 8px 12px;
            background: #008374;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }

        .search-btn:hover {
            background: #006b5e;
        }

        .contact-item {
            display: flex;
            align-items: center;
            padding: 10px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
            position: relative;
        }

            .contact-item:hover {
                background-color: #e2f3f1;
            }

            .contact-item img {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                margin-right: 10px;
            }

        .notification-badge {
            position: absolute;
            right: 10px;
            top: 15px;
            background-color: #ff4b4b;
            color: white;
            padding: 2px 6px;
            font-size: 11px;
            border-radius: 10px;
        }
        .message-container {
    margin-bottom: 15px;
    max-width: 75%;
    padding: 10px;
    border-radius: 10px;
    background-color: #f1f1f1;
}

.sent {
    background-color: #d1f5e0;
    align-self: flex-end;
    margin-left: auto;
}

.received {
    background-color: #fff;
    border: 1px solid #ddd;
    margin-right: auto;
}

.message-header {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.message-header .avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: 10px;
}

.message-body p {
    margin: 0;
    font-size: 14px;
}

.attachment-link {
    display: inline-block;
    margin-top: 5px;
    color: #008374;
    font-weight: bold;
}

        /* Nouveaux styles pour les fonctionnalités améliorées */
        .message-search {
            padding: 10px;
            background: #f8f9fa;
            border-bottom: 1px solid #ddd;
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .close-search-btn {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 8px;
            cursor: pointer;
        }

        .emoji-toolbar {
            display: flex;
            gap: 5px;
            padding: 8px;
            background: #f8f9fa;
            border-bottom: 1px solid #ddd;
            flex-wrap: wrap;
        }

        .emoji-btn {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            padding: 5px;
            border-radius: 4px;
            transition: background 0.2s;
        }

        .emoji-btn:hover {
            background: #e9ecef;
        }

        .search-toggle-btn {
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 8px;
            cursor: pointer;
            font-size: 14px;
        }

        .search-toggle-btn:hover {
            background: #5a6268;
        }

        .message-input-container {
            display: flex;
            flex-direction: column;
            flex: 1;
            gap: 8px;
        }

        .message-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            resize: vertical;
            min-height: 60px;
            font-family: inherit;
        }

        .attachment-section {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .file-input {
            display: none;
        }

        .file-label {
            background: #6c757d;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.2s;
        }

        .file-label:hover {
            background: #5a6268;
        }

        .file-name {
            font-size: 12px;
            color: #6c757d;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .message-compose {
            display: flex;
            flex-direction: column;
            gap: 0;
        }

        .send-btn {
            background: #008374;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
            align-self: flex-end;
            margin-top: 8px;
        }

        .send-btn:hover {
            background: #006b5e;
        }

        /* Styles pour les pièces jointes dans les messages */
        .attachment-container {
            display: flex;
            align-items: center;
            gap: 8px;
            background: #f8f9fa;
            padding: 8px 12px;
            border-radius: 6px;
            margin-top: 8px;
            border-left: 3px solid #008374;
        }

        .attachment-info {
            font-size: 11px;
            color: #6c757d;
            font-style: italic;
        }

        .attachment-link {
            color: #008374 !important;
            text-decoration: none;
            font-weight: 500;
        }

        .attachment-link:hover {
            text-decoration: underline;
        }

        /* Amélioration des messages */
        .message-container.sent {
            margin-left: 20%;
        }

        .message-container.received {
            margin-right: 20%;
        }

        .message-container.sent .message-body {
            background: #008374;
            color: white;
        }

        .message-container.received .message-body {
            background: #f1f3f4;
            color: #333;
        }

        .message-body {
            padding: 12px;
            border-radius: 12px;
            margin-top: 5px;
        }

        /* Indicateur de typing (pour future implémentation) */
        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 5px;
            padding: 10px;
            font-style: italic;
            color: #6c757d;
        }

        .typing-dots {
            display: flex;
            gap: 2px;
        }

        .typing-dots span {
            width: 6px;
            height: 6px;
            background: #6c757d;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dots span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dots span:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
            }
            30% {
                transform: translateY(-10px);
            }
        }

    </style>

    <script src="Scripts/messagerie.js"></script>
    <script>
        // Configuration spécifique à cette page
        document.addEventListener('DOMContentLoaded', function() {
            // Configurer les IDs des contrôles ASP.NET
            window.MessagerieControls = {
                txtMessage: '<%= txtMessage.ClientID %>',
                btnenvoie: '<%= btnenvoie.ClientID %>'
                // Contrôles avancés temporairement désactivés
                // fileUpload: '<%= fileUpload.ClientID %>',
                // lblFileName: '<%= lblFileName.ClientID %>',
                // txtRechercheContact: '<%= txtRechercheContact.ClientID %>',
                // btnRechercheContact: '<%= btnRechercheContact.ClientID %>',
                // txtRechercheMessage: '<%= txtRechercheMessage.ClientID %>'
            };
        });
    </script>

</asp:Content>
