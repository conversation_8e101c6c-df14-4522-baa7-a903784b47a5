﻿using LinCom.Classe;
using LinCom.file;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.Helpers;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class MembreImp : IMembre
    {
        int msg;
        private Membre m = new Membre();
        ICommonCode co = new CommonCode();
        public void AfficherDetails(long membreId, Membre_Class membre)
        {
            using (Connection con = new Connection())
            {
                var m = con.Membres.FirstOrDefault(x => x.MembreId == membreId);
                if (m != null)
                {
                    membre.MembreId = m.MembreId;
                    membre.Nom = m.Nom;
                    membre.Prenom = m.Prenom;
                    membre.Email = m.Email;
                    membre.Telephone = m.Telephone;
                    membre.Sexe = m.Sexe;
                    membre.DateNaissance = m.DateNaissance;
                    membre.ProvinceId = m.ProvinceId;
                    membre.CommuneId = m.CommuneId;
                    membre.CreatedAt = m.CreatedAt;
                    membre.name = m.name;
                    membre.province = m.province;
                    membre.username = m.username;
                    membre.motpasse = m.motpasse;
                    membre.statut = m.statut;
                    membre.IsActive = m.IsActive;
                    membre.IsVerified = m.IsVerified;
                    membre.LastLogin = m.LastLogin;
                    membre.RoleMembreID = m.RoleMembreID;
                    membre.PhotoProfil = m.PhotoProfil;
                    membre.facebook = m.facebook;
                    membre.siteweb = m.siteweb;
                    membre.twitter = m.twitter;
                    membre.instagramme = m.instagramme;
                    membre.linkedin = m.linkedin;
                    membre.youtube = m.youtube;
                    membre.Biographie = m.Biographie;
                    membre.DateInscription = m.DateInscription;
                    membre.ResetToken = m.ResetToken;
                    membre.ResetTokenExpiry = m.ResetTokenExpiry;
                    membre.Adresse = m.Adresse;
                    membre.LanguePreferee = m.LanguePreferee;
                    
    }
            }
        }
        public int Connect(Membre_Class membre, string usernm, string pwsd, int code)
        {
            using (Connection con = new Connection())
            {
                m = con.Membres.Where(x => x.username == usernm && x.statut == "actif").FirstOrDefault();

                if (m != null)
                {
                    membre.MembreId = m.MembreId;
                    membre.Nom = m.Nom;
                    membre.Prenom = m.Prenom;
                    membre.Email = m.Email;
                    membre.Telephone = m.Telephone;
                    membre.Sexe = m.Sexe;
                    membre.DateNaissance = m.DateNaissance;
                    membre.ProvinceId = m.ProvinceId;
                    membre.CommuneId = m.CommuneId;
                    membre.name = m.name;
                    membre.province = m.province;
                    membre.username = m.username;
                    membre.motpasse = m.motpasse;
                    membre.statut = m.statut;
                    membre.IsActive = m.IsActive;
                    membre.IsVerified = m.IsVerified;
                    membre.LastLogin = m.LastLogin;
                    membre.RoleMembreID = m.RoleMembreID;
                    membre.PhotoProfil = m.PhotoProfil;
                    membre.ResetToken = m.ResetToken;
                    membre.ResetTokenExpiry = m.ResetTokenExpiry;
                    membre.Adresse = m.Adresse;
                    membre.LanguePreferee = m.LanguePreferee;


                    return msg = 1;
                }
                else return msg = 0;

            }
         
        }

     
        public bool VerifierMotDePasse(string email, string motDePasseEntree)
        {
            using (Connection con = new Connection())
            {
                var membre = con.Membres.FirstOrDefault(m => m.username == email);
                if (membre != null)
                {
                    return BCrypt.Net.BCrypt.Verify(motDePasseEntree, membre.motpasse);
                }
            }
            return false;
        }

        public void AfficherDetails(string name, Membre_Class membre,int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    var m = con.Membres.FirstOrDefault(x => x.name == name);
                    if (m != null)
                    {
                        membre.MembreId = m.MembreId;
                        membre.Nom = m.Nom;
                        membre.Prenom = m.Prenom;
                        membre.Email = m.Email;
                        membre.Telephone = m.Telephone;
                        membre.Sexe = m.Sexe;
                        membre.DateNaissance = m.DateNaissance;
                        membre.ProvinceId = m.ProvinceId;
                        membre.CommuneId = m.CommuneId;
                        membre.CreatedAt = m.CreatedAt;
                        membre.name = m.name;
                        membre.province = m.province;
                        membre.username = m.username;
                        membre.motpasse = m.motpasse;
                        membre.statut = m.statut;
                        membre.IsActive = m.IsActive;
                        membre.IsVerified = m.IsVerified;
                        membre.LastLogin = m.LastLogin;
                        membre.RoleMembreID = m.RoleMembreID;
                        membre.PhotoProfil = m.PhotoProfil;
                        membre.facebook = m.facebook;
                        membre.siteweb = m.siteweb;
                        membre.twitter = m.twitter;
                        membre.instagramme = m.instagramme;
                        membre.linkedin = m.linkedin;
                        membre.youtube = m.youtube;
                        membre.Biographie = m.Biographie;
                        membre.DateInscription = m.DateInscription;
                        membre.ResetToken = m.ResetToken;
                        membre.ResetTokenExpiry = m.ResetTokenExpiry;
                        membre.Adresse = m.Adresse;
                        membre.LanguePreferee = m.LanguePreferee;
                    }
                    else membre.name="No name";

                }
                else if(cd==1)
                {
                    var m = con.Membres.FirstOrDefault(x => x.Email == name);
                    if (m != null)
                    {
                        membre.MembreId = m.MembreId;
                        membre.Nom = m.Nom;
                        membre.Prenom = m.Prenom;
                        membre.Email = m.Email;
                        membre.Telephone = m.Telephone;
                        membre.Sexe = m.Sexe;
                        membre.DateNaissance = m.DateNaissance;
                        membre.ProvinceId = m.ProvinceId;
                        membre.CommuneId = m.CommuneId;
                        membre.CreatedAt = m.CreatedAt;
                        membre.name = m.name;
                        membre.province = m.province;
                        membre.username = m.username;
                        membre.motpasse = m.motpasse;
                        membre.statut = m.statut;
                        membre.IsActive = m.IsActive;
                        membre.IsVerified = m.IsVerified;
                        membre.LastLogin = m.LastLogin;
                        membre.RoleMembreID = m.RoleMembreID;
                        membre.PhotoProfil = m.PhotoProfil;
                        membre.facebook = m.facebook;
                        membre.siteweb = m.siteweb;
                        membre.twitter = m.twitter;
                        membre.instagramme = m.instagramme;
                        membre.linkedin = m.linkedin;
                        membre.youtube = m.youtube;
                        membre.Biographie = m.Biographie;
                        membre.DateInscription = m.DateInscription;
                        membre.ResetToken = m.ResetToken;
                        membre.ResetTokenExpiry = m.ResetTokenExpiry;
                        membre.Adresse = m.Adresse;
                        membre.LanguePreferee = m.LanguePreferee;
                    }
                    else membre.Email = "No email";
                }
                 
           
            
            }
        }

        public int Ajouter(Membre_Class membre)
        {
            using (Connection con = new Connection())
            {
                m.Nom = membre.Nom;
                m.Prenom = membre.Prenom;
                m.Email = membre.Email;
                m.Telephone = membre.Telephone;
                m.Sexe = membre.Sexe;
                m.DateNaissance = membre.DateNaissance;
                m.ProvinceId = membre.ProvinceId;
                m.CommuneId = membre.CommuneId;
                m.name = membre.name;
                m.province = membre.province;
                m.commune = membre.commune;
               
                m.username = membre.username;
                m.motpasse = membre.motpasse;
                m.statut = membre.statut;
                m.IsActive = membre.IsActive;
                m.IsVerified = membre.IsVerified;
                m.LastLogin = membre.LastLogin;
                m.RoleMembreID = membre.RoleMembreID;

                m.PhotoProfil = membre.PhotoProfil;
                m.facebook = membre.facebook;
                m.siteweb = membre.siteweb;
                m.twitter = membre.twitter;
                m.instagramme = membre.instagramme;
                m.linkedin = membre.linkedin;
                m.youtube = membre.youtube;
                m.Biographie = membre.Biographie;
                m.DateInscription = membre.DateInscription;
                m.ResetToken = membre.ResetToken;
                m.ResetTokenExpiry = membre.ResetTokenExpiry;
                m.Adresse = membre.Adresse;
                m.LanguePreferee = membre.LanguePreferee;



                try
                {
                    con.Membres.Add(m);
                    if (con.SaveChanges() == 1)
                    {
                        con.Membres.Add(m);

                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch
                {
                    return msg = 0;
                }
            }
        }

        public void ChargerGridView(GridView gdv, string filtre = "", string name = "")
        {
            using (Connection con = new Connection())
            {
                var query = from m in con.Membres
                            select new
                            {
                                MembreId = m.MembreId,
                                Nom = m.Nom,
                                Prenom = m.Prenom,
                                Email = m.Email,
                                Telephone = m.Telephone,
                                Sexe = m.Sexe,
                                DateNaissance = m.DateNaissance,
                                ProvinceId = m.ProvinceId,
                                CommuneId = m.CommuneId,
                                CreatedAt = m.CreatedAt,
                                name = m.name,
                                province = m.province,
                                username = m.username,
                                motpasse = m.motpasse,
                                statut = m.statut,
                                IsActive = m.IsActive,
                                IsVerified = m.IsVerified,
                                LastLogin = m.LastLogin,
                                RoleMembreID = m.RoleMembreID,
                                PhotoProfil = m.PhotoProfil,
                                facebook = m.facebook,
                                siteweb = m.siteweb,
                                twitter = m.twitter,
                                instagramme = m.instagramme,
                                linkedin = m.linkedin,
                                youtube = m.youtube,
                                Biographie = m.Biographie,
                                DateInscription = m.DateInscription,
                                ResetToken = m.ResetToken,
                                ResetTokenExpiry = m.ResetTokenExpiry,
                                Adresse = m.Adresse,
                                LanguePreferee = m.LanguePreferee,

                            }
            ;

                if (!string.IsNullOrEmpty(filtre))
                {
                    query = query.Where(x => x.Nom.Contains(filtre) ||
                                           x.Prenom.Contains(filtre) ||
                                           x.Email.Contains(filtre));
                }

                gdv.DataSource = query.ToList();
                gdv.DataBind();
            }
        }

        public void ChargerListview(ListView gdv, long id,string statut, string name)
        {
            using (Connection con = new Connection())
            {
                var query = from m in con.Membres
                            where m.statut==statut
                            orderby m.Nom descending
                            select new
                            {
                                id = m.MembreId,
                                Nom = m.Nom,
                                Prenom = m.Prenom,
                                Membre=m.Nom+" "+m.Prenom,
                                Email = m.Email,
                                Telephone = m.Telephone,
                                Sexe = m.Sexe,
                                DateNaissance = m.DateNaissance,
                                ProvinceId = m.ProvinceId,
                                CommuneId = m.CommuneId,
                                CreatedAt = m.CreatedAt,
                                name = m.name,
                                province = m.province,
                                username = m.username,
                                motpasse = m.motpasse,
                                statut = m.statut,
                                IsActive = m.IsActive,
                                IsVerified = m.IsVerified,
                                LastLogin = m.LastLogin,
                                RoleMembreID = m.RoleMembreID,
                                PhotoProfil = m.PhotoProfil,
                                facebook = m.facebook,
                                siteweb = m.siteweb,
                                twitter = m.twitter,
                                instagramme = m.instagramme,
                                linkedin = m.linkedin,
                                youtube = m.youtube,
                                Biographie = m.Biographie,
                                DateInscription = m.DateInscription,
                                ResetToken = m.ResetToken,
                                ResetTokenExpiry = m.ResetTokenExpiry,
                                Adresse = m.Adresse,
                                LanguePreferee = m.LanguePreferee,


                            }
            ;

                gdv.DataSource = query.ToList();
                gdv.DataBind();
            }
        }

        public int Modifier(Membre_Class membre,string email, long code,int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    var m = con.Membres.FirstOrDefault(x => x.MembreId == code);
                    if (m != null)
                    {
                        m.Nom = membre.Nom;
                        m.Prenom = membre.Prenom;
                        m.Email = membre.Email;
                        m.Telephone = membre.Telephone;
                        m.Sexe = membre.Sexe;
                        m.DateNaissance = membre.DateNaissance;
                        m.ProvinceId = membre.ProvinceId;
                        m.CommuneId = membre.CommuneId;
                        m.name = membre.name;
                        m.province = membre.province;
                        m.commune = membre.commune;
                        m.Adresse = membre.Adresse;


                        m.Nom = membre.Nom;
                        m.Prenom = membre.Prenom;
                        m.Email = membre.Email;
                        m.Telephone = membre.Telephone;
                        m.Sexe =membre.Sexe;
                        m.DateNaissance = membre.DateNaissance;
                        m.ProvinceId =membre.ProvinceId;
                        m.CommuneId = membre.CommuneId;
                        m.name = membre.name;
                        m.province = membre.province;
                        m.commune = membre.commune;
                        m.Adresse = membre.Adresse;
                        m.username = membre.username;
                        m.motpasse = membre.motpasse;
                        m.statut = membre.statut;

                        m.RoleMembreID = membre.RoleMembreID;

                        m.PhotoProfil = membre.PhotoProfil;

                        m.Biographie = membre.Biographie;
                        try
                        {
                            if (con.SaveChanges() == 1)
                            {
                                return msg = 1;
                            }
                            else
                                return msg = 0;
                        }
                        catch
                        {
                            return msg = 0;
                        }
                    }
                    return msg = 0;
                }
                else if (cd==1)
                {
                    var m = con.Membres.FirstOrDefault(x => x.username == email);
                    if (m != null)
                    {
                        m.ResetToken = membre.ResetToken;
                        m.ResetTokenExpiry = membre.ResetTokenExpiry;
                       
                        try
                        {
                            if (con.SaveChanges() == 1)
                            {
                                return msg = 1;
                            }
                            else
                                return msg = 0;
                        }
                        catch
                        {
                            return msg = 0;
                        }
                    }
                  
                }
                else if (cd == 2)
                {
                    var m = con.Membres.FirstOrDefault(x => x.ResetToken == email && x.ResetTokenExpiry > DateTime.Now);
                    if (m != null)
                    {
                        m.motpasse = membre.motpasse;
                        m.ResetToken = null;
                        m.ResetTokenExpiry = null;

                        try
                        {
                            if (con.SaveChanges() == 1)
                            {
                                return msg = 1;
                            }
                            else
                                return msg = 0;
                        }
                        catch
                        {
                            return msg = 0;
                        }
                    }

                }
                return msg;
            }
        }
        public int count(int cd, string publie, string code)
        {
            int n = 0;
            using (Connection con = new Connection())
            {
                if (cd == 0)
                {
                    var b = (from p in con.Membres

                             select p).Count();
                    n = b;
                }
                else if (cd == 1)
                {
                    var b = (from p in con.Membres

                             where p.statut == publie
                             select p).Count();
                    n = b;
                }

            }
            return n;
        }
        public int Supprimer(long membreId)
        {
            using (Connection con = new Connection())
            {
                var m = con.Membres.FirstOrDefault(x => x.MembreId == membreId);
                if (m != null)
                {
                    con.Membres.Remove(m);
                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }

        public void chargerMembre(DropDownList lst)
        {
            lst.Items.Clear();
            using (Connection con = new Connection())
            {
                var obj = (from p in con.Membres
                           where p.statut == "actif"
                           select p).ToList();

                if (obj != null && obj.Count() > 0)
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "";
                    item0.Text = "-- Sélectionnez un membre --";
                    lst.Items.Add(item0);

                    foreach (var data in obj)
                    {
                        ListItem item = new ListItem();
                        item.Value = data.MembreId.ToString();
                        item.Text = data.Nom + " " + data.Prenom + " (" + data.Email + ")";
                        lst.Items.Add(item);
                    }
                }
                else
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "";
                    item0.Text = "Aucun membre disponible";
                    lst.Items.Add(item0);
                }
            }
        }

        /// <summary>
        /// Recherche des membres par nom, prénom ou email
        /// </summary>
        public void RechercherMembres(ListView gdv, string motCle, string statut = "actif")
        {
            using (Connection con = new Connection())
            {
                var query = from m in con.Membres
                            where m.statut == statut &&
                                  (m.Nom.Contains(motCle) ||
                                   m.Prenom.Contains(motCle) ||
                                   m.Email.Contains(motCle) ||
                                   (m.Nom + " " + m.Prenom).Contains(motCle))
                            orderby m.Nom
                            select new
                            {
                                id = m.MembreId,
                                Nom = m.Nom,
                                Prenom = m.Prenom,
                                Membre = m.Nom + " " + m.Prenom,
                                Email = m.Email,
                                Telephone = m.Telephone,
                                PhotoProfil = m.PhotoProfil ?? "default-avatar.png",
                                statut = m.statut,
                                IsActive = m.IsActive,
                                LastLogin = m.LastLogin,
                                province = m.province,
                                commune = m.commune
                            };

                gdv.DataSource = query.ToList();
                gdv.DataBind();
            }
        }

        /// <summary>
        /// Obtient les membres récemment actifs pour la messagerie
        /// </summary>
        public void ChargerMembresRecents(ListView gdv, long membreConnecteId, int limite = 20)
        {
            using (Connection con = new Connection())
            {
                var query = from m in con.Membres
                            where m.statut == "actif" && m.MembreId != membreConnecteId
                            orderby m.LastLogin descending
                            select new
                            {
                                id = m.MembreId,
                                Nom = m.Nom,
                                Prenom = m.Prenom,
                                Membre = m.Nom + " " + m.Prenom,
                                Email = m.Email,
                                PhotoProfil = m.PhotoProfil ?? "default-avatar.png",
                                LastLogin = m.LastLogin,
                                IsActive = m.IsActive,
                                province = m.province,
                                commune = m.commune,
                                EstEnLigne = m.LastLogin.HasValue && m.LastLogin.Value > DateTime.Now.AddMinutes(-15)
                            };

                gdv.DataSource = query.Take(limite).ToList();
                gdv.DataBind();
            }
        }
    }
}