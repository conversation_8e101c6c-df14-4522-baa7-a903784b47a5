<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.web>
    <!-- Autoriser l'accès aux fichiers de messagerie pour les utilisateurs authentifiés -->
    <authorization>
      <deny users="?" />
      <allow users="*" />
    </authorization>
    
    <!-- Configuration pour les types MIME -->
    <httpHandlers>
      <add verb="*" path="*" type="System.Web.StaticFileHandler" />
    </httpHandlers>
  </system.web>
  
  <system.webServer>
    <!-- Sécurité pour IIS -->
    <security>
      <requestFiltering>
        <!-- Limiter la taille des fichiers à 10MB -->
        <requestLimits maxAllowedContentLength="10485760" />
      </requestFiltering>
    </security>
    
    <!-- Types MIME pour différents types de fichiers -->
    <staticContent>
      <mimeMap fileExtension=".pdf" mimeType="application/pdf" />
      <mimeMap fileExtension=".doc" mimeType="application/msword" />
      <mimeMap fileExtension=".docx" mimeType="application/vnd.openxmlformats-officedocument.wordprocessingml.document" />
      <mimeMap fileExtension=".xls" mimeType="application/vnd.ms-excel" />
      <mimeMap fileExtension=".xlsx" mimeType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" />
      <mimeMap fileExtension=".ppt" mimeType="application/vnd.ms-powerpoint" />
      <mimeMap fileExtension=".pptx" mimeType="application/vnd.openxmlformats-officedocument.presentationml.presentation" />
    </staticContent>
  </system.webServer>
</configuration>
