//------------------------------------------------------------------------------
// <auto-generated>
//     Ce code a été généré par un outil.
//
//     Les modifications apportées à ce fichier peuvent provoquer un comportement incorrect et seront perdues si
//     le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

namespace LinCom
{


    public partial class test_messagerie
    {

        /// <summary>
        /// Contrôle form1.
        /// </summary>
        /// <remarks>
        /// Champ généré automatiquement.
        /// Pour modifier, déplacez la déclaration de champ du fichier de concepteur vers le fichier code-behind.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlForm form1;

        /// <summary>
        /// Contrôle fileUploadTest.
        /// </summary>
        /// <remarks>
        /// Champ généré automatiquement.
        /// Pour modifier, déplacez la déclaration de champ du fichier de concepteur vers le fichier code-behind.
        /// </remarks>
        protected global::System.Web.UI.WebControls.FileUpload fileUploadTest;

        /// <summary>
        /// Contrôle btnTestFile.
        /// </summary>
        /// <remarks>
        /// Champ généré automatiquement.
        /// Pour modifier, déplacez la déclaration de champ du fichier de concepteur vers le fichier code-behind.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Button btnTestFile;

        /// <summary>
        /// Contrôle lblFileResult.
        /// </summary>
        /// <remarks>
        /// Champ généré automatiquement.
        /// Pour modifier, déplacez la déclaration de champ du fichier de concepteur vers le fichier code-behind.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Label lblFileResult;

        /// <summary>
        /// Contrôle txtEmojiTest.
        /// </summary>
        /// <remarks>
        /// Champ généré automatiquement.
        /// Pour modifier, déplacez la déclaration de champ du fichier de concepteur vers le fichier code-behind.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox txtEmojiTest;

        /// <summary>
        /// Contrôle btnTestEmoji.
        /// </summary>
        /// <remarks>
        /// Champ généré automatiquement.
        /// Pour modifier, déplacez la déclaration de champ du fichier de concepteur vers le fichier code-behind.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Button btnTestEmoji;

        /// <summary>
        /// Contrôle lblEmojiResult.
        /// </summary>
        /// <remarks>
        /// Champ généré automatiquement.
        /// Pour modifier, déplacez la déclaration de champ du fichier de concepteur vers le fichier code-behind.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Label lblEmojiResult;

        /// <summary>
        /// Contrôle txtCleanTest.
        /// </summary>
        /// <remarks>
        /// Champ généré automatiquement.
        /// Pour modifier, déplacez la déclaration de champ du fichier de concepteur vers le fichier code-behind.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox txtCleanTest;

        /// <summary>
        /// Contrôle btnTestClean.
        /// </summary>
        /// <remarks>
        /// Champ généré automatiquement.
        /// Pour modifier, déplacez la déclaration de champ du fichier de concepteur vers le fichier code-behind.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Button btnTestClean;

        /// <summary>
        /// Contrôle lblCleanResult.
        /// </summary>
        /// <remarks>
        /// Champ généré automatiquement.
        /// Pour modifier, déplacez la déclaration de champ du fichier de concepteur vers le fichier code-behind.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Label lblCleanResult;

        /// <summary>
        /// Contrôle txtFileNameTest.
        /// </summary>
        /// <remarks>
        /// Champ généré automatiquement.
        /// Pour modifier, déplacez la déclaration de champ du fichier de concepteur vers le fichier code-behind.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox txtFileNameTest;

        /// <summary>
        /// Contrôle btnTestFileName.
        /// </summary>
        /// <remarks>
        /// Champ généré automatiquement.
        /// Pour modifier, déplacez la déclaration de champ du fichier de concepteur vers le fichier code-behind.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Button btnTestFileName;

        /// <summary>
        /// Contrôle lblFileNameResult.
        /// </summary>
        /// <remarks>
        /// Champ généré automatiquement.
        /// Pour modifier, déplacez la déclaration de champ du fichier de concepteur vers le fichier code-behind.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Label lblFileNameResult;

        /// <summary>
        /// Contrôle txtIconTest.
        /// </summary>
        /// <remarks>
        /// Champ généré automatiquement.
        /// Pour modifier, déplacez la déclaration de champ du fichier de concepteur vers le fichier code-behind.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox txtIconTest;

        /// <summary>
        /// Contrôle btnTestIcon.
        /// </summary>
        /// <remarks>
        /// Champ généré automatiquement.
        /// Pour modifier, déplacez la déclaration de champ du fichier de concepteur vers le fichier code-behind.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Button btnTestIcon;

        /// <summary>
        /// Contrôle lblIconResult.
        /// </summary>
        /// <remarks>
        /// Champ généré automatiquement.
        /// Pour modifier, déplacez la déclaration de champ du fichier de concepteur vers le fichier code-behind.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Label lblIconResult;

        /// <summary>
        /// Contrôle txtSizeTest.
        /// </summary>
        /// <remarks>
        /// Champ généré automatiquement.
        /// Pour modifier, déplacez la déclaration de champ du fichier de concepteur vers le fichier code-behind.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox txtSizeTest;

        /// <summary>
        /// Contrôle btnTestSize.
        /// </summary>
        /// <remarks>
        /// Champ généré automatiquement.
        /// Pour modifier, déplacez la déclaration de champ du fichier de concepteur vers le fichier code-behind.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Button btnTestSize;

        /// <summary>
        /// Contrôle lblSizeResult.
        /// </summary>
        /// <remarks>
        /// Champ généré automatiquement.
        /// Pour modifier, déplacez la déclaration de champ du fichier de concepteur vers le fichier code-behind.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Label lblSizeResult;

        /// <summary>
        /// Contrôle lblSystemInfo.
        /// </summary>
        /// <remarks>
        /// Champ généré automatiquement.
        /// Pour modifier, déplacez la déclaration de champ du fichier de concepteur vers le fichier code-behind.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Label lblSystemInfo;
    }
}
