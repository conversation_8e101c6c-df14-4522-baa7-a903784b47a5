using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using System.Xml.Linq;

namespace LinCom.Classe
{
    /// <summary>
    /// Classe pour gérer la configuration de la messagerie
    /// </summary>
    public class MessageConfig_Class
    {
        private static MessageConfig_Class _instance;
        private static readonly object _lock = new object();
        private XDocument _config;
        private DateTime _lastLoaded;

        // Singleton pattern
        public static MessageConfig_Class Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new MessageConfig_Class();
                        }
                    }
                }
                return _instance;
            }
        }

        private MessageConfig_Class()
        {
            LoadConfiguration();
        }

        private void LoadConfiguration()
        {
            try
            {
                string configPath = HttpContext.Current.Server.MapPath("~/App_Data/MessageConfig.xml");
                if (File.Exists(configPath))
                {
                    _config = XDocument.Load(configPath);
                    _lastLoaded = DateTime.Now;
                }
                else
                {
                    // Créer une configuration par défaut si le fichier n'existe pas
                    CreateDefaultConfiguration();
                }
            }
            catch (Exception)
            {
                // En cas d'erreur, utiliser la configuration par défaut
                CreateDefaultConfiguration();
            }
        }

        private void CreateDefaultConfiguration()
        {
            _config = new XDocument(
                new XElement("MessageConfiguration",
                    new XElement("General",
                        new XElement("MaxFileSize", "10485760"),
                        new XElement("MaxMessageLength", "1000"),
                        new XElement("MessagesPerPage", "20"),
                        new XElement("EnableEmojis", "true"),
                        new XElement("EnableFileAttachments", "true")
                    )
                )
            );
            _lastLoaded = DateTime.Now;
        }

        /// <summary>
        /// Recharge la configuration si nécessaire
        /// </summary>
        private void RefreshIfNeeded()
        {
            // Recharger la configuration toutes les 5 minutes
            if (DateTime.Now.Subtract(_lastLoaded).TotalMinutes > 5)
            {
                LoadConfiguration();
            }
        }

        /// <summary>
        /// Obtient la taille maximale autorisée pour les fichiers
        /// </summary>
        public long MaxFileSize
        {
            get
            {
                RefreshIfNeeded();
                try
                {
                    return long.Parse(_config.Root.Element("General").Element("MaxFileSize").Value);
                }
                catch
                {
                    return 10485760; // 10MB par défaut
                }
            }
        }

        /// <summary>
        /// Obtient la longueur maximale des messages
        /// </summary>
        public int MaxMessageLength
        {
            get
            {
                RefreshIfNeeded();
                try
                {
                    return int.Parse(_config.Root.Element("General").Element("MaxMessageLength").Value);
                }
                catch
                {
                    return 1000; // 1000 caractères par défaut
                }
            }
        }

        /// <summary>
        /// Obtient le nombre de messages par page
        /// </summary>
        public int MessagesPerPage
        {
            get
            {
                RefreshIfNeeded();
                try
                {
                    return int.Parse(_config.Root.Element("General").Element("MessagesPerPage").Value);
                }
                catch
                {
                    return 20; // 20 messages par défaut
                }
            }
        }

        /// <summary>
        /// Vérifie si les emojis sont activés
        /// </summary>
        public bool EnableEmojis
        {
            get
            {
                RefreshIfNeeded();
                try
                {
                    return bool.Parse(_config.Root.Element("General").Element("EnableEmojis").Value);
                }
                catch
                {
                    return true; // Activé par défaut
                }
            }
        }

        /// <summary>
        /// Vérifie si les pièces jointes sont activées
        /// </summary>
        public bool EnableFileAttachments
        {
            get
            {
                RefreshIfNeeded();
                try
                {
                    return bool.Parse(_config.Root.Element("General").Element("EnableFileAttachments").Value);
                }
                catch
                {
                    return true; // Activé par défaut
                }
            }
        }

        /// <summary>
        /// Obtient la liste des extensions de fichiers autorisées
        /// </summary>
        public List<string> AllowedFileExtensions
        {
            get
            {
                RefreshIfNeeded();
                try
                {
                    var extensions = _config.Root.Element("AllowedFileTypes")
                                           .Elements("FileType")
                                           .Select(x => x.Attribute("extension").Value)
                                           .ToList();
                    return extensions;
                }
                catch
                {
                    // Extensions par défaut
                    return new List<string> { ".jpg", ".jpeg", ".png", ".pdf", ".doc", ".docx", ".txt" };
                }
            }
        }

        /// <summary>
        /// Obtient l'icône pour un type de fichier
        /// </summary>
        public string GetFileIcon(string extension)
        {
            RefreshIfNeeded();
            try
            {
                var fileType = _config.Root.Element("AllowedFileTypes")
                                         .Elements("FileType")
                                         .FirstOrDefault(x => x.Attribute("extension").Value.Equals(extension, StringComparison.OrdinalIgnoreCase));
                
                return fileType?.Attribute("icon")?.Value ?? "📄";
            }
            catch
            {
                return "📄"; // Icône par défaut
            }
        }

        /// <summary>
        /// Obtient le dictionnaire des emojis
        /// </summary>
        public Dictionary<string, string> GetEmojis()
        {
            RefreshIfNeeded();
            try
            {
                var emojis = _config.Root.Element("Emojis")
                                       .Elements("Emoji")
                                       .ToDictionary(
                                           x => x.Attribute("shortcut").Value,
                                           x => x.Attribute("unicode").Value
                                       );
                return emojis;
            }
            catch
            {
                // Emojis par défaut
                return new Dictionary<string, string>
                {
                    {":)", "😊"}, {":(", "😢"}, {":D", "😃"}, {";)", "😉"},
                    {":P", "😛"}, {":|", "😐"}, {":o", "😮"}, {"<3", "❤️"}
                };
            }
        }

        /// <summary>
        /// Obtient un message système
        /// </summary>
        public string GetSystemMessage(string key, params object[] args)
        {
            RefreshIfNeeded();
            try
            {
                var message = _config.Root.Element("SystemMessages")
                                        .Elements("Message")
                                        .FirstOrDefault(x => x.Attribute("key").Value == key);
                
                if (message != null)
                {
                    string text = message.Attribute("text").Value;
                    return args.Length > 0 ? string.Format(text, args) : text;
                }
            }
            catch
            {
                // Ignorer les erreurs et retourner le message par défaut
            }

            // Messages par défaut
            switch (key)
            {
                case "file_too_large":
                    return $"Le fichier est trop volumineux (maximum {args.FirstOrDefault()}MB)";
                case "file_type_not_allowed":
                    return $"Type de fichier non autorisé. Extensions autorisées: {args.FirstOrDefault()}";
                case "message_too_long":
                    return $"Le message est trop long (maximum {args.FirstOrDefault()} caractères)";
                case "no_recipient":
                    return "Veuillez sélectionner un destinataire";
                case "empty_message":
                    return "Le message ne peut pas être vide";
                default:
                    return "Erreur inconnue";
            }
        }

        /// <summary>
        /// Vérifie si une extension de fichier est autorisée
        /// </summary>
        public bool IsFileExtensionAllowed(string extension)
        {
            return AllowedFileExtensions.Contains(extension.ToLower());
        }

        /// <summary>
        /// Obtient les balises HTML dangereuses à filtrer
        /// </summary>
        public List<string> DangerousHtmlTags
        {
            get
            {
                RefreshIfNeeded();
                try
                {
                    var tags = _config.Root.Element("Security")
                                         .Element("DangerousHtmlTags")
                                         .Elements("Tag")
                                         .Select(x => x.Value)
                                         .ToList();
                    return tags;
                }
                catch
                {
                    // Tags dangereux par défaut
                    return new List<string> { "script", "iframe", "object", "embed", "form" };
                }
            }
        }

        /// <summary>
        /// Vérifie si la protection XSS est activée
        /// </summary>
        public bool EnableXSSProtection
        {
            get
            {
                RefreshIfNeeded();
                try
                {
                    return bool.Parse(_config.Root.Element("Security").Element("EnableXSSProtection").Value);
                }
                catch
                {
                    return true; // Activé par défaut
                }
            }
        }
    }
}
